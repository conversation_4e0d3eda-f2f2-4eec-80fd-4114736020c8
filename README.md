# AirMonitor - WPF MVVM 应用程序框架

这是一个基于现代.NET技术栈构建的WPF应用程序框架，展示了如何使用MVVM模式、依赖注入、日志系统和配置管理来构建可维护的桌面应用程序。

## 🏗️ 项目结构

```
AirMonitor/
├── Configuration/          # 配置管理
│   └── AppSettings.cs     # 配置模型类
├── Core/                  # 核心组件
│   ├── BaseViewModel.cs   # ViewModel基类
│   ├── ViewModelLocator.cs # ViewModel定位器
│   ├── LoggingExtensions.cs # 日志配置扩展
│   └── ServiceCollectionExtensions.cs # 服务注册扩展
├── Models/                # 数据模型
├── Services/              # 服务层
│   ├── IDialogService.cs  # 对话框服务接口
│   ├── DialogService.cs   # 对话框服务实现
│   ├── IDataService.cs    # 数据服务接口
│   ├── IConfigurationService.cs # 配置服务接口
│   ├── ConfigurationService.cs  # 配置服务实现
│   └── InputDialog.xaml   # 输入对话框
├── ViewModels/            # 视图模型
│   └── MainViewModel.cs   # 主窗口ViewModel
├── Views/                 # 视图
├── App.xaml              # 应用程序入口
├── App.xaml.cs           # 应用程序代码
├── MainWindow.xaml       # 主窗口XAML
├── MainWindow.xaml.cs    # 主窗口代码
└── appsettings.json      # 配置文件
```

## 🚀 技术栈

- **.NET 8.0** - 最新的.NET框架
- **WPF** - Windows Presentation Foundation
- **CommunityToolkit.Mvvm** - 现代MVVM框架
- **Microsoft.Extensions.DependencyInjection** - 依赖注入容器
- **Microsoft.Extensions.Hosting** - 主机构建器
- **Serilog** - 结构化日志系统
- **Microsoft.Extensions.Configuration** - 配置管理

## ✨ 主要特性

### 1. MVVM架构
- 使用CommunityToolkit.Mvvm提供的现代MVVM支持
- BaseViewModel基类提供通用功能
- ViewModelLocator用于在XAML中获取ViewModel实例

### 2. 依赖注入
- 完整的DI容器配置
- 服务生命周期管理
- 松耦合的架构设计

### 3. 日志系统
- Serilog结构化日志
- 专为WPF优化的文件日志输出
- Debug模式下的Visual Studio输出窗口支持
- 应用内日志查看器服务
- 可配置的日志级别和滚动策略

### 4. 配置管理
- appsettings.json配置文件
- 强类型配置模型
- 运行时配置重载支持

### 5. 服务层
- 对话框服务：统一的UI交互
- 数据服务：通用的CRUD操作接口
- 配置服务：配置读写管理
- 日志查看器服务：应用内日志查看和管理

## 🎯 核心组件说明

### BaseViewModel
提供ViewModel的基础功能：
- IsBusy状态管理
- 异步操作执行
- 状态消息显示
- 错误处理

### DialogService
统一的对话框服务：
- 信息、警告、错误对话框
- 确认对话框
- 输入对话框
- 文件选择对话框

### 配置系统
- 支持JSON配置文件
- 强类型配置绑定
- 分层配置结构

## 🔧 使用方法

### 1. 克隆项目
```bash
git clone <repository-url>
cd AirMonitor
```

### 2. 构建项目
```bash
dotnet build
```

### 3. 运行项目
```bash
dotnet run
```

### 4. 添加新的ViewModel
```csharp
public partial class YourViewModel : BaseViewModel
{
    public YourViewModel(ILogger<YourViewModel> logger) : base(logger)
    {
        Title = "Your Title";
    }
    
    [RelayCommand]
    private async Task YourCommandAsync()
    {
        await ExecuteAsync(async () =>
        {
            // 你的业务逻辑
        });
    }
}
```

### 5. 注册服务
在`ServiceCollectionExtensions.cs`中添加：
```csharp
services.AddTransient<YourViewModel>();
services.AddScoped<IYourService, YourService>();
```

## 📝 配置文件

`appsettings.json`包含以下配置节：
- **Logging**: 日志配置
- **Application**: 应用程序基本信息
- **Database**: 数据库连接配置
- **UI**: 用户界面配置

## 🎨 UI设计

- 现代化的Material Design风格
- 响应式布局
- 状态指示器
- 进度条显示

## 📚 最佳实践

1. **MVVM分离**: 严格遵循MVVM模式，保持View和ViewModel的分离
2. **依赖注入**: 使用DI容器管理对象生命周期
3. **异步编程**: 使用async/await处理异步操作
4. **错误处理**: 统一的错误处理和用户反馈
5. **日志记录**: 在关键位置添加日志记录
6. **配置管理**: 使用配置文件管理应用程序设置

## 🔄 扩展指南

### 添加新页面
1. 在`Views`文件夹中创建XAML文件
2. 在`ViewModels`文件夹中创建对应的ViewModel
3. 在服务注册中添加新的ViewModel
4. 在ViewModelLocator中添加属性（如需要）

### 添加新服务
1. 定义服务接口
2. 实现服务类
3. 在`ServiceCollectionExtensions`中注册服务

## 📄 许可证

本项目仅用于学习和演示目的。

---

**注意**: 这是一个演示框架，实际项目中可能需要根据具体需求进行调整和扩展。
