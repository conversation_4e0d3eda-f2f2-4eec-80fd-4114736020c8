﻿<dx:ThemedWindow
    x:Class="AirMonitor.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:dxn="http://schemas.devexpress.com/winfx/2008/xaml/navigation"
    xmlns:local="clr-namespace:AirMonitor"
    xmlns:views="clr-namespace:AirMonitor.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="{Binding Title}"
    Width="1200"
    Height="800"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 顶部工具栏 -->
        <dx:ToolBarControl Grid.Row="0">
            <dx:BarButtonItem
                Content="主页"
                Glyph="{dx:DXImage 'SvgImages/Icon Builder/Actions_Home.svg'}"
                Command="{Binding NavigateToHomeCommand}" />
            <dx:BarButtonItem
                Content="DevExpress 演示"
                Glyph="{dx:DXImage 'SvgImages/Icon Builder/Actions_Show.svg'}"
                Command="{Binding NavigateToDemoCommand}" />
            <dx:BarItemSeparator />
            <dx:BarButtonItem
                Content="设置"
                Glyph="{dx:DXImage 'SvgImages/Icon Builder/Actions_Settings.svg'}"
                Command="{Binding ShowSettingsCommand}" />
            <dx:BarButtonItem
                Content="关于"
                Glyph="{dx:DXImage 'SvgImages/Icon Builder/Actions_About.svg'}"
                Command="{Binding ShowAboutCommand}" />
        </dx:ToolBarControl>

        <!-- 主要内容区域 -->
        <dxn:TabControlNavigation
            Grid.Row="1"
            x:Name="MainTabControl">

            <!-- 主页标签 -->
            <dxn:TabNavigationItem Header="主页" Glyph="{dx:DXImage 'SvgImages/Icon Builder/Actions_Home.svg'}">
                <Grid Margin="20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!-- 欢迎信息 -->
                    <dx:GroupBox
                        Grid.Row="0"
                        Header="欢迎使用 AirMonitor"
                        Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock
                                FontSize="16"
                                FontWeight="Bold"
                                Text="这是一个集成了 DevExpress WPF 24.2 的现代化应用程序框架" />
                            <TextBlock
                                Margin="0,10,0,0"
                                Text="技术栈：.NET 8.0 + WPF + MVVM + DevExpress + 依赖注入 + Serilog"
                                TextWrapping="Wrap" />
                        </StackPanel>
                    </dx:GroupBox>

                    <!-- 快速操作 -->
                    <dx:GroupBox
                        Grid.Row="1"
                        Header="快速操作"
                        Margin="0,0,0,20">
                        <UniformGrid Columns="3" Rows="1">
                            <dx:SimpleButton
                                Margin="5"
                                Padding="10"
                                Command="{Binding NavigateToDemoCommand}"
                                Content="查看 DevExpress 演示" />
                            <dx:SimpleButton
                                Margin="5"
                                Padding="10"
                                Command="{Binding ShowSettingsCommand}"
                                Content="打开设置" />
                            <dx:SimpleButton
                                Margin="5"
                                Padding="10"
                                Command="{Binding ShowAboutCommand}"
                                Content="关于应用程序" />
                        </UniformGrid>
                    </dx:GroupBox>

                    <!-- 状态信息 -->
                    <dx:GroupBox
                        Grid.Row="2"
                        Header="系统状态">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="10">
                                <TextBlock Text="应用程序信息:" FontWeight="Bold" Margin="0,0,0,10" />
                                <TextBlock Text="{Binding ApplicationName}" />
                                <TextBlock Text="{Binding ApplicationVersion}" />
                                <TextBlock Text="{Binding Environment}" />
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="10">
                                <TextBlock Text="运行状态:" FontWeight="Bold" Margin="0,0,0,10" />
                                <TextBlock Text="{Binding StatusMessage}" />
                                <TextBlock Text="{Binding CurrentTime}" />
                            </StackPanel>
                        </Grid>
                    </dx:GroupBox>
                </Grid>
            </dxn:TabNavigationItem>

            <!-- DevExpress 演示标签 -->
            <dxn:TabNavigationItem Header="DevExpress 演示" Glyph="{dx:DXImage 'SvgImages/Icon Builder/Actions_Show.svg'}">
                <views:DevExpressDemoView DataContext="{Binding DevExpressDemoViewModel}" />
            </dxn:TabNavigationItem>

        </dxn:TabControlNavigation>

        <!-- 状态栏 -->
        <dx:StatusBarControl Grid.Row="2">
            <dx:StatusBarItem Content="{Binding StatusMessage}" />
            <dx:StatusBarItem Content="{Binding CurrentTime}" HorizontalAlignment="Right" />
        </dx:StatusBarControl>
    </Grid>
</dx:ThemedWindow>
