using Microsoft.Extensions.Logging;
using System.Globalization;
using System.IO;
using System.Text.RegularExpressions;

namespace AirMonitor.Services;

/// <summary>
/// 日志查看器服务实现
/// </summary>
public class LogViewerService : ILogViewerService
{
    private readonly ILogger<LogViewerService> _logger;
    private readonly IConfigurationService _configurationService;
    private readonly string _logDirectory;

    public LogViewerService(ILogger<LogViewerService> logger, IConfigurationService configurationService)
    {
        _logger = logger;
        _configurationService = configurationService;
        
        var logPath = _configurationService.GetValue("Logging:File:Path", "Logs/airmonitor-.log");
        _logDirectory = Path.GetDirectoryName(logPath) ?? "Logs";
        
        // 确保日志目录存在
        if (!Directory.Exists(_logDirectory))
        {
            Directory.CreateDirectory(_logDirectory);
        }
    }

    public async Task<IEnumerable<LogEntry>> GetRecentLogsAsync(int count = 100)
    {
        try
        {
            var logFiles = await GetAllLogFilePathsAsync();
            var allEntries = new List<LogEntry>();

            // 从最新的文件开始读取
            foreach (var filePath in logFiles.OrderByDescending(f => f))
            {
                var entries = await ReadLogEntriesFromFileAsync(filePath);
                allEntries.AddRange(entries);
                
                if (allEntries.Count >= count)
                    break;
            }

            return allEntries
                .OrderByDescending(e => e.Timestamp)
                .Take(count)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取最新日志时发生错误");
            return Enumerable.Empty<LogEntry>();
        }
    }

    public async Task<IEnumerable<LogEntry>> GetLogsByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        try
        {
            var logFiles = await GetAllLogFilePathsAsync();
            var allEntries = new List<LogEntry>();

            foreach (var filePath in logFiles)
            {
                var entries = await ReadLogEntriesFromFileAsync(filePath);
                var filteredEntries = entries.Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate);
                allEntries.AddRange(filteredEntries);
            }

            return allEntries.OrderByDescending(e => e.Timestamp).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按日期范围获取日志时发生错误");
            return Enumerable.Empty<LogEntry>();
        }
    }

    public async Task<IEnumerable<LogEntry>> GetLogsByLevelAsync(LogLevel level, int count = 100)
    {
        try
        {
            var recentLogs = await GetRecentLogsAsync(count * 2); // 获取更多日志以便筛选
            return recentLogs
                .Where(e => e.Level == level)
                .Take(count)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "按日志级别获取日志时发生错误");
            return Enumerable.Empty<LogEntry>();
        }
    }

    public async Task<IEnumerable<LogEntry>> SearchLogsAsync(string searchText, int count = 100)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return Enumerable.Empty<LogEntry>();

            var recentLogs = await GetRecentLogsAsync(count * 3); // 获取更多日志以便搜索
            return recentLogs
                .Where(e => e.Message.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                           e.SourceContext.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                           (e.Exception?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false))
                .Take(count)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "搜索日志时发生错误");
            return Enumerable.Empty<LogEntry>();
        }
    }

    public async Task<int> CleanupOldLogsAsync(int daysToKeep = 7)
    {
        try
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            var logFiles = Directory.GetFiles(_logDirectory, "*.log");
            var deletedCount = 0;

            foreach (var filePath in logFiles)
            {
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.CreationTime < cutoffDate)
                {
                    File.Delete(filePath);
                    deletedCount++;
                    _logger.LogInformation("删除旧日志文件: {FilePath}", filePath);
                }
            }

            return deletedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理旧日志文件时发生错误");
            return 0;
        }
    }

    public string GetCurrentLogFilePath()
    {
        var logPath = _configurationService.GetValue("Logging:File:Path", "Logs/airmonitor-.log");
        var today = DateTime.Now.ToString("yyyyMMdd");
        return logPath.Replace("-.", $"-{today}.");
    }

    public async Task<IEnumerable<string>> GetAllLogFilePathsAsync()
    {
        try
        {
            if (!Directory.Exists(_logDirectory))
                return Enumerable.Empty<string>();

            var logFiles = Directory.GetFiles(_logDirectory, "*.log")
                .OrderByDescending(f => new FileInfo(f).CreationTime)
                .ToList();

            return logFiles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取日志文件路径时发生错误");
            return Enumerable.Empty<string>();
        }
    }

    private async Task<IEnumerable<LogEntry>> ReadLogEntriesFromFileAsync(string filePath)
    {
        var entries = new List<LogEntry>();
        
        try
        {
            if (!File.Exists(filePath))
                return entries;

            var lines = await File.ReadAllLinesAsync(filePath);
            
            foreach (var line in lines)
            {
                var entry = ParseLogEntry(line);
                if (entry != null)
                {
                    entries.Add(entry);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取日志文件时发生错误: {FilePath}", filePath);
        }

        return entries;
    }

    private LogEntry? ParseLogEntry(string logLine)
    {
        try
        {
            // 解析日志格式: [2025-06-19 10:15:30.123] [INF] [SourceContext] Message
            var pattern = @"^\[([^\]]+)\]\s*\[([^\]]+)\]\s*\[([^\]]*)\]\s*(.*)$";
            var match = Regex.Match(logLine, pattern);
            
            if (!match.Success)
                return null;

            var timestampStr = match.Groups[1].Value;
            var levelStr = match.Groups[2].Value;
            var sourceContext = match.Groups[3].Value;
            var message = match.Groups[4].Value;

            if (!DateTime.TryParseExact(timestampStr, "yyyy-MM-dd HH:mm:ss.fff", 
                CultureInfo.InvariantCulture, DateTimeStyles.None, out var timestamp))
            {
                return null;
            }

            var level = ParseLogLevel(levelStr);

            return new LogEntry
            {
                Timestamp = timestamp,
                Level = level,
                SourceContext = sourceContext,
                Message = message
            };
        }
        catch
        {
            return null;
        }
    }

    private LogLevel ParseLogLevel(string levelStr)
    {
        return levelStr.ToUpperInvariant() switch
        {
            "VRB" => LogLevel.Verbose,
            "DBG" => LogLevel.Debug,
            "INF" => LogLevel.Information,
            "WRN" => LogLevel.Warning,
            "ERR" => LogLevel.Error,
            "FTL" => LogLevel.Fatal,
            _ => LogLevel.Information
        };
    }
}
