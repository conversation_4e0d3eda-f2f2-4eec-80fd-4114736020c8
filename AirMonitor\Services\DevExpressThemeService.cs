using DevExpress.Xpf.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace AirMonitor.Services;

/// <summary>
/// DevExpress主题服务实现
/// </summary>
public class DevExpressThemeService : IDevExpressThemeService
{
    private readonly ILogger<DevExpressThemeService> _logger;
    private readonly IConfiguration _configuration;
    private string _currentThemeName;

    // 可用主题列表
    private static readonly ReadOnlyDictionary<string, string> _availableThemes = new(
        new Dictionary<string, string>
        {
            { "Office2019Colorful", "Office 2019 彩色" },
            { "Office2019Black", "Office 2019 黑色" },
            { "Office2019White", "Office 2019 白色" },
            { "Office2019HighContrast", "Office 2019 高对比度" },
            { "VS2019Blue", "Visual Studio 2019 蓝色" },
            { "VS2019Dark", "Visual Studio 2019 深色" },
            { "VS2019Light", "Visual Studio 2019 浅色" },
            { "Win10Light", "Windows 10 浅色" },
            { "Win10Dark", "Windows 10 深色" },
            { "MaterialLight", "Material 浅色" },
            { "MaterialDark", "Material 深色" }
        });

    public DevExpressThemeService(ILogger<DevExpressThemeService> logger, IConfiguration configuration)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        // 从配置中获取默认主题，如果没有配置则使用Office2019Colorful
        _currentThemeName = _configuration["UI:DevExpressTheme"] ?? "Office2019Colorful";
        
        _logger.LogInformation("DevExpress主题服务已初始化，当前主题: {ThemeName}", _currentThemeName);
    }

    public string CurrentThemeName => _currentThemeName;

    public IEnumerable<string> AvailableThemes => _availableThemes.Keys;

    public event EventHandler<string>? ThemeChanged;

    public void ApplyTheme(string themeName)
    {
        if (string.IsNullOrWhiteSpace(themeName))
        {
            _logger.LogWarning("尝试应用空的主题名称");
            return;
        }

        if (!_availableThemes.ContainsKey(themeName))
        {
            _logger.LogWarning("未知的主题名称: {ThemeName}", themeName);
            return;
        }

        try
        {
            // 应用DevExpress主题
            ApplicationThemeHelper.ApplicationThemeName = themeName;
            
            var previousTheme = _currentThemeName;
            _currentThemeName = themeName;
            
            _logger.LogInformation("主题已从 {PreviousTheme} 切换到 {NewTheme}", previousTheme, themeName);
            
            // 触发主题变更事件
            ThemeChanged?.Invoke(this, themeName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用主题 {ThemeName} 时发生错误", themeName);
            throw;
        }
    }

    public void ApplyDefaultTheme()
    {
        var defaultTheme = _configuration["UI:DevExpressTheme"] ?? "Office2019Colorful";
        ApplyTheme(defaultTheme);
    }

    public string GetThemeDisplayName(string themeName)
    {
        return _availableThemes.TryGetValue(themeName, out var displayName) ? displayName : themeName;
    }
}
