﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;E:\DevExpress 23.2\Components\Offline Packages;e:\DevExpress 24.2\Components\Offline Packages;E:\Microsoft Visual Studio\Shared\NuGetPackages;D:\Syncfusion\Essential Studio\WPF\29.1.33\ToolboxNuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="E:\DevExpress 23.2\Components\Offline Packages\" />
    <SourceRoot Include="e:\DevExpress 24.2\Components\Offline Packages\" />
    <SourceRoot Include="E:\Microsoft Visual Studio\Shared\NuGetPackages\" />
    <SourceRoot Include="D:\Syncfusion\Essential Studio\WPF\29.1.33\ToolboxNuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.props" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.usersecrets\9.0.6\buildTransitive\net8.0\Microsoft.Extensions.Configuration.UserSecrets.props')" />
    <Import Project="e:\DevExpress 24.2\Components\Offline Packages\devexpress.data.desktop\24.2.3\build\net8.0-windows\DevExpress.Data.Desktop.props" Condition="Exists('e:\DevExpress 24.2\Components\Offline Packages\devexpress.data.desktop\24.2.3\build\net8.0-windows\DevExpress.Data.Desktop.props')" />
    <Import Project="e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.core\24.2.3\build\net8.0-windows\DevExpress.Wpf.Core.props" Condition="Exists('e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.core\24.2.3\build\net8.0-windows\DevExpress.Wpf.Core.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgDevExpress_Data Condition=" '$(PkgDevExpress_Data)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.data\24.2.3</PkgDevExpress_Data>
    <PkgDevExpress_Xpo Condition=" '$(PkgDevExpress_Xpo)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.xpo\24.2.3</PkgDevExpress_Xpo>
    <PkgDevExpress_Wpf_Core Condition=" '$(PkgDevExpress_Wpf_Core)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.core\24.2.3</PkgDevExpress_Wpf_Core>
    <PkgDevExpress_Wpf_Ribbon Condition=" '$(PkgDevExpress_Wpf_Ribbon)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.ribbon\24.2.3</PkgDevExpress_Wpf_Ribbon>
    <PkgDevExpress_Wpf_LayoutControl Condition=" '$(PkgDevExpress_Wpf_LayoutControl)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.layoutcontrol\24.2.3</PkgDevExpress_Wpf_LayoutControl>
    <PkgDevExpress_Wpf_Grid_Core Condition=" '$(PkgDevExpress_Wpf_Grid_Core)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.grid.core\24.2.3</PkgDevExpress_Wpf_Grid_Core>
    <PkgDevExpress_Wpf_Controls Condition=" '$(PkgDevExpress_Wpf_Controls)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.controls\24.2.3</PkgDevExpress_Wpf_Controls>
    <PkgDevExpress_Wpf_DocumentViewer_Core Condition=" '$(PkgDevExpress_Wpf_DocumentViewer_Core)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.documentviewer.core\24.2.3</PkgDevExpress_Wpf_DocumentViewer_Core>
    <PkgDevExpress_Wpf_Docking Condition=" '$(PkgDevExpress_Wpf_Docking)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.docking\24.2.3</PkgDevExpress_Wpf_Docking>
    <PkgDevExpress_Wpf_Printing Condition=" '$(PkgDevExpress_Wpf_Printing)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.printing\24.2.3</PkgDevExpress_Wpf_Printing>
    <PkgDevExpress_Wpf_RichEdit Condition=" '$(PkgDevExpress_Wpf_RichEdit)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.richedit\24.2.3</PkgDevExpress_Wpf_RichEdit>
    <PkgDevExpress_Wpf_PropertyGrid Condition=" '$(PkgDevExpress_Wpf_PropertyGrid)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.propertygrid\24.2.3</PkgDevExpress_Wpf_PropertyGrid>
    <PkgDevExpress_Wpf_Grid_Printing Condition=" '$(PkgDevExpress_Wpf_Grid_Printing)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.grid.printing\24.2.3</PkgDevExpress_Wpf_Grid_Printing>
    <PkgDevExpress_DataAccess Condition=" '$(PkgDevExpress_DataAccess)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.dataaccess\24.2.3</PkgDevExpress_DataAccess>
    <PkgDevExpress_Wpf_ExpressionEditor Condition=" '$(PkgDevExpress_Wpf_ExpressionEditor)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.expressioneditor\24.2.3</PkgDevExpress_Wpf_ExpressionEditor>
    <PkgDevExpress_Wpf_Grid Condition=" '$(PkgDevExpress_Wpf_Grid)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.grid\24.2.3</PkgDevExpress_Wpf_Grid>
    <PkgDevExpress_Wpf_Charts Condition=" '$(PkgDevExpress_Wpf_Charts)' == '' ">e:\DevExpress 24.2\Components\Offline Packages\devexpress.wpf.charts\24.2.3</PkgDevExpress_Wpf_Charts>
  </PropertyGroup>
</Project>