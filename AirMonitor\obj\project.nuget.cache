{"version": 2, "dgSpecHash": "eHrOO6z3zok=", "success": true, "projectFilePath": "D:\\01 AirMonitor\\AirMonitor\\AirMonitor.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.4.0\\communitytoolkit.mvvm.8.4.0.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.charts.core\\24.2.3\\devexpress.charts.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.codeparser\\24.2.3\\devexpress.codeparser.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.data\\24.2.3\\devexpress.data.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.data.desktop\\24.2.3\\devexpress.data.desktop.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.dataaccess\\24.2.3\\devexpress.dataaccess.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.datavisualization.core\\24.2.3\\devexpress.datavisualization.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.drawing\\24.2.3\\devexpress.drawing.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.images\\24.2.3\\devexpress.images.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.mvvm\\24.2.3\\devexpress.mvvm.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.office.core\\24.2.3\\devexpress.office.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pdf.core\\24.2.3\\devexpress.pdf.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pdf.drawing\\24.2.3\\devexpress.pdf.drawing.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.printing.core\\24.2.3\\devexpress.printing.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.richedit.core\\24.2.3\\devexpress.richedit.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.charts\\24.2.3\\devexpress.wpf.charts.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.controls\\24.2.3\\devexpress.wpf.controls.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.core\\24.2.3\\devexpress.wpf.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.docking\\24.2.3\\devexpress.wpf.docking.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.documentviewer.core\\24.2.3\\devexpress.wpf.documentviewer.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.expressioneditor\\24.2.3\\devexpress.wpf.expressioneditor.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.grid\\24.2.3\\devexpress.wpf.grid.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.grid.core\\24.2.3\\devexpress.wpf.grid.core.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.grid.printing\\24.2.3\\devexpress.wpf.grid.printing.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.layoutcontrol\\24.2.3\\devexpress.wpf.layoutcontrol.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.office\\24.2.3\\devexpress.wpf.office.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.printing\\24.2.3\\devexpress.wpf.printing.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.propertygrid\\24.2.3\\devexpress.wpf.propertygrid.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.ribbon\\24.2.3\\devexpress.wpf.ribbon.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.richedit\\24.2.3\\devexpress.wpf.richedit.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.all\\24.2.3\\devexpress.wpf.themes.all.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.dxstyle\\24.2.3\\devexpress.wpf.themes.dxstyle.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.lightgray\\24.2.3\\devexpress.wpf.themes.lightgray.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.metropolisdark\\24.2.3\\devexpress.wpf.themes.metropolisdark.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.metropolislight\\24.2.3\\devexpress.wpf.themes.metropolislight.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2007black\\24.2.3\\devexpress.wpf.themes.office2007black.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2007blue\\24.2.3\\devexpress.wpf.themes.office2007blue.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2007silver\\24.2.3\\devexpress.wpf.themes.office2007silver.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2010black\\24.2.3\\devexpress.wpf.themes.office2010black.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2010blue\\24.2.3\\devexpress.wpf.themes.office2010blue.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2010silver\\24.2.3\\devexpress.wpf.themes.office2010silver.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2013\\24.2.3\\devexpress.wpf.themes.office2013.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2013darkgray\\24.2.3\\devexpress.wpf.themes.office2013darkgray.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2013lightgray\\24.2.3\\devexpress.wpf.themes.office2013lightgray.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2016black\\24.2.3\\devexpress.wpf.themes.office2016black.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2016blackse\\24.2.3\\devexpress.wpf.themes.office2016blackse.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2016colorful\\24.2.3\\devexpress.wpf.themes.office2016colorful.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2016colorfulse\\24.2.3\\devexpress.wpf.themes.office2016colorfulse.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2016darkgrayse\\24.2.3\\devexpress.wpf.themes.office2016darkgrayse.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2016white\\24.2.3\\devexpress.wpf.themes.office2016white.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2016whitese\\24.2.3\\devexpress.wpf.themes.office2016whitese.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2019black\\24.2.3\\devexpress.wpf.themes.office2019black.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2019colorful\\24.2.3\\devexpress.wpf.themes.office2019colorful.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2019darkgray\\24.2.3\\devexpress.wpf.themes.office2019darkgray.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2019highcontrast\\24.2.3\\devexpress.wpf.themes.office2019highcontrast.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.office2019white\\24.2.3\\devexpress.wpf.themes.office2019white.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.seven\\24.2.3\\devexpress.wpf.themes.seven.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.touchlinedark\\24.2.3\\devexpress.wpf.themes.touchlinedark.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.vs2010\\24.2.3\\devexpress.wpf.themes.vs2010.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.vs2017blue\\24.2.3\\devexpress.wpf.themes.vs2017blue.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.vs2017dark\\24.2.3\\devexpress.wpf.themes.vs2017dark.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.vs2017light\\24.2.3\\devexpress.wpf.themes.vs2017light.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.vs2019blue\\24.2.3\\devexpress.wpf.themes.vs2019blue.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.vs2019dark\\24.2.3\\devexpress.wpf.themes.vs2019dark.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.vs2019light\\24.2.3\\devexpress.wpf.themes.vs2019light.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.win10dark\\24.2.3\\devexpress.wpf.themes.win10dark.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.win10light\\24.2.3\\devexpress.wpf.themes.win10light.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.win11dark\\24.2.3\\devexpress.wpf.themes.win11dark.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.wpf.themes.win11light\\24.2.3\\devexpress.wpf.themes.win11light.24.2.3.nupkg.sha512", "e:\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.xpo\\24.2.3\\devexpress.xpo.24.2.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.6\\microsoft.extensions.configuration.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.6\\microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.6\\microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\9.0.6\\microsoft.extensions.configuration.commandline.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\9.0.6\\microsoft.extensions.configuration.environmentvariables.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.6\\microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.6\\microsoft.extensions.configuration.json.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\9.0.6\\microsoft.extensions.configuration.usersecrets.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.6\\microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.6\\microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.0\\microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\9.0.6\\microsoft.extensions.diagnostics.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\9.0.6\\microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.6\\microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.6\\microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.6\\microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\9.0.6\\microsoft.extensions.hosting.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\9.0.6\\microsoft.extensions.hosting.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.6\\microsoft.extensions.logging.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.6\\microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.6\\microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\9.0.6\\microsoft.extensions.logging.console.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\9.0.6\\microsoft.extensions.logging.debug.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\9.0.6\\microsoft.extensions.logging.eventlog.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\9.0.6\\microsoft.extensions.logging.eventsource.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\6.0.16\\microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.6\\microsoft.extensions.options.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.6\\microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.6\\microsoft.extensions.primitives.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.4\\microsoft.netcore.platforms.3.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\4.7.0\\microsoft.win32.systemevents.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.3.0\\serilog.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.environment\\3.0.1\\serilog.enrichers.environment.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.enrichers.thread\\4.0.0\\serilog.enrichers.thread.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\9.0.0\\serilog.extensions.hosting.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\9.0.0\\serilog.extensions.logging.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\9.0.0\\serilog.settings.configuration.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\3.0.0\\serilog.sinks.debug.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\7.0.0\\serilog.sinks.file.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\4.4.0\\system.codedom.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\8.0.1\\system.configuration.configurationmanager.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\8.0.1\\system.data.oledb.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.6\\system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.6\\system.diagnostics.eventlog.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\8.0.1\\system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\4.7.2\\system.drawing.common.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\9.0.6\\system.io.pipelines.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\8.0.0\\system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\4.7.0\\system.security.principal.windows.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\8.0.0\\system.servicemodel.http.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.netframingbase\\6.2.0\\system.servicemodel.netframingbase.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\6.2.0\\system.servicemodel.nettcp.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\8.0.0\\system.servicemodel.primitives.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\9.0.6\\system.text.encodings.web.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.6\\system.text.json.9.0.6.nupkg.sha512"], "logs": []}