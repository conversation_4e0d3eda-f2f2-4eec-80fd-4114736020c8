<UserControl
    x:Class="AirMonitor.Views.DevExpressDemoView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
    xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
    xmlns:dxc="http://schemas.devexpress.com/winfx/2008/xaml/charts"
    xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
    xmlns:local="clr-namespace:AirMonitor.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="800"
    d:DesignWidth="1200"
    mc:Ignorable="d">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 标题和主题选择 -->
        <dx:GroupBox
            Grid.Row="0"
            Header="DevExpress 控件演示"
            Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="200" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    FontSize="16"
                    FontWeight="Bold"
                    Text="{Binding Title}" />

                <TextBlock
                    Grid.Column="1"
                    Margin="10,0"
                    VerticalAlignment="Center"
                    Text="当前主题:" />

                <dxe:ComboBoxEdit
                    Grid.Column="2"
                    DisplayMember="DisplayName"
                    ItemsSource="{Binding AvailableThemes}"
                    SelectedValue="{Binding SelectedTheme, Mode=TwoWay}"
                    ValueMember="Name" />

                <dx:SimpleButton
                    Grid.Column="3"
                    Margin="10,0,0,0"
                    Command="{Binding ChangeThemeCommand}"
                    Content="应用主题" />
            </Grid>
        </dx:GroupBox>

        <!-- 主要内容区域 -->
        <dx:GroupBox
            Grid.Row="1"
            Header="控件演示">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="300" />
                </Grid.RowDefinitions>

                <!-- 数据网格区域 -->
                <dx:GroupBox
                    Grid.Row="0"
                    Header="数据网格 (GridControl)"
                    Margin="0,0,0,5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <!-- 工具栏 -->
                        <StackPanel
                            Grid.Row="0"
                            Margin="0,0,0,10"
                            Orientation="Horizontal">
                            <dx:SimpleButton
                                Command="{Binding AddDataItemCommand}"
                                Content="添加数据"
                                Margin="0,0,10,0" />
                            <dx:SimpleButton
                                Command="{Binding RemoveDataItemCommand}"
                                Content="删除选中"
                                Margin="0,0,10,0" />
                            <dx:SimpleButton
                                Command="{Binding ShowGridDetailsCommand}"
                                Content="查看详情" />
                        </StackPanel>

                        <!-- 数据网格 -->
                        <dxg:GridControl
                            Grid.Row="1"
                            ItemsSource="{Binding GridData}"
                            SelectedItem="{Binding SelectedGridItem, Mode=TwoWay}">
                            <dxg:GridControl.Columns>
                                <dxg:GridColumn
                                    FieldName="Id"
                                    Header="ID"
                                    Width="60" />
                                <dxg:GridColumn
                                    FieldName="Name"
                                    Header="名称"
                                    Width="150" />
                                <dxg:GridColumn
                                    FieldName="Value"
                                    Header="数值"
                                    Width="100">
                                    <dxg:GridColumn.EditSettings>
                                        <dxe:SpinEditSettings DisplayFormat="F2" />
                                    </dxg:GridColumn.EditSettings>
                                </dxg:GridColumn>
                                <dxg:GridColumn
                                    FieldName="Date"
                                    Header="日期"
                                    Width="120">
                                    <dxg:GridColumn.EditSettings>
                                        <dxe:DateEditSettings DisplayFormat="yyyy-MM-dd" />
                                    </dxg:GridColumn.EditSettings>
                                </dxg:GridColumn>
                                <dxg:GridColumn
                                    FieldName="Status"
                                    Header="状态"
                                    Width="100" />
                            </dxg:GridControl.Columns>
                            <dxg:GridControl.View>
                                <dxg:TableView
                                    AllowEditing="False"
                                    AutoWidth="True"
                                    ShowGroupPanel="False" />
                            </dxg:GridControl.View>
                        </dxg:GridControl>
                    </Grid>
                </dx:GroupBox>

                <!-- 图表区域 -->
                <dx:GroupBox
                    Grid.Row="1"
                    Header="图表控件 (ChartControl)"
                    Margin="0,5,0,0">
                    <dxc:ChartControl>
                        <dxc:ChartControl.Diagram>
                            <dxc:XYDiagram2D>
                                <dxc:XYDiagram2D.AxisX>
                                    <dxc:AxisX2D>
                                        <dxc:AxisX2D.Label>
                                            <dxc:AxisLabel Angle="45" />
                                        </dxc:AxisX2D.Label>
                                    </dxc:AxisX2D>
                                </dxc:XYDiagram2D.AxisX>
                            </dxc:XYDiagram2D>
                        </dxc:ChartControl.Diagram>

                        <dxc:BarSeries2D
                            ArgumentDataMember="Category"
                            DataSource="{Binding ChartData}"
                            DisplayName="数据值"
                            ValueDataMember="Value">
                            <dxc:BarSeries2D.Label>
                                <dxc:SeriesLabel />
                            </dxc:BarSeries2D.Label>
                        </dxc:BarSeries2D>

                        <dxc:ChartControl.Legend>
                            <dxc:Legend HorizontalPosition="Right" />
                        </dxc:ChartControl.Legend>
                    </dxc:ChartControl>
                </dx:GroupBox>
            </Grid>
        </dx:GroupBox>

        <!-- 状态栏 -->
        <dx:StatusBarControl
            Grid.Row="1"
            VerticalAlignment="Bottom">
            <dx:StatusBarItem Content="{Binding StatusMessage}" />
            <dx:StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="当前主题: " />
                    <TextBlock Text="{Binding CurrentThemeName}" FontWeight="Bold" />
                </StackPanel>
            </dx:StatusBarItem>
        </dx:StatusBarControl>
    </Grid>
</UserControl>
