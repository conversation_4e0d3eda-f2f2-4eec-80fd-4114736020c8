using DevExpress.Xpf.Core;

namespace AirMonitor.Services;

/// <summary>
/// DevExpress主题服务接口
/// </summary>
public interface IDevExpressThemeService
{
    /// <summary>
    /// 获取当前主题名称
    /// </summary>
    string CurrentThemeName { get; }

    /// <summary>
    /// 获取所有可用主题
    /// </summary>
    IEnumerable<string> AvailableThemes { get; }

    /// <summary>
    /// 应用主题
    /// </summary>
    /// <param name="themeName">主题名称</param>
    void ApplyTheme(string themeName);

    /// <summary>
    /// 应用默认主题
    /// </summary>
    void ApplyDefaultTheme();

    /// <summary>
    /// 获取主题显示名称
    /// </summary>
    /// <param name="themeName">主题名称</param>
    /// <returns>显示名称</returns>
    string GetThemeDisplayName(string themeName);

    /// <summary>
    /// 主题变更事件
    /// </summary>
    event EventHandler<string>? ThemeChanged;
}
