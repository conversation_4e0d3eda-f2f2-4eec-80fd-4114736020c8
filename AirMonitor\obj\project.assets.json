{"version": 3, "targets": {"net8.0-windows7.0": {"CommunityToolkit.Mvvm/8.4.0": {"type": "package", "compile": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/CommunityToolkit.Mvvm.dll": {"related": ".pdb;.xml"}}, "build": {"buildTransitive/CommunityToolkit.Mvvm.targets": {}}}, "DevExpress.Charts.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]"}, "compile": {"lib/net8.0/DevExpress.Charts.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Charts.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.CodeParser/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "System.CodeDom": "4.4.0"}, "compile": {"lib/net8.0/DevExpress.CodeParser.v24.2.dll": {}}, "runtime": {"lib/net8.0/DevExpress.CodeParser.v24.2.dll": {}}}, "DevExpress.Data/24.2.3": {"type": "package", "dependencies": {"System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Data.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Data.Desktop/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "System.Data.OleDb": "8.0.1"}, "compile": {"lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.dll": {"related": ".xml"}}, "build": {"build/net8.0-windows/DevExpress.Data.Desktop.props": {}}}, "DevExpress.DataAccess/24.2.3": {"type": "package", "dependencies": {"DevExpress.CodeParser": "[24.2.3]", "DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Office.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.RichEdit.Core": "[24.2.3]", "DevExpress.Xpo": "[24.2.3]", "System.Configuration.ConfigurationManager": "8.0.1", "System.Data.SqlClient": "4.8.6"}, "compile": {"lib/net8.0/DevExpress.DataAccess.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.DataAccess.v24.2.dll": {"related": ".xml"}}}, "DevExpress.DataVisualization.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]"}, "compile": {"lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll": {}}, "runtime": {"lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll": {}}}, "DevExpress.Drawing/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Drawing.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Images/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Images.v24.2.dll": {}}, "runtime": {"lib/net8.0/DevExpress.Images.v24.2.dll": {}}}, "DevExpress.Mvvm/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Mvvm.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Mvvm.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Office.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Office.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1"}, "compile": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Pdf.Drawing/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll": {"related": ".xml"}}}, "DevExpress.Printing.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Pdf.Drawing": "[24.2.3]", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0"}, "compile": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Printing.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.RichEdit.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Office.Core": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "System.Drawing.Common": "4.7.2"}, "compile": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Charts/24.2.3": {"type": "package", "dependencies": {"DevExpress.Charts.Core": "[24.2.3]", "DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.DataVisualization.Core": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.Docking": "[24.2.3]", "DevExpress.Wpf.Grid.Core": "[24.2.3]", "DevExpress.Wpf.Printing": "[24.2.3]", "DevExpress.Wpf.PropertyGrid": "[24.2.3]", "DevExpress.Wpf.Ribbon": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Charts.Designer.v24.2.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Charts.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Charts.Designer.v24.2.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Charts.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Controls/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Themes.Office2019Colorful": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.dll": {"related": ".xml"}}, "build": {"build/net8.0-windows/DevExpress.Wpf.Core.props": {}}}, "DevExpress.Wpf.Docking/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Wpf.DocumentViewer.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Controls": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.Grid.Core": "[24.2.3]", "DevExpress.Wpf.Ribbon": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll": {"related": ".xml"}}}, "DevExpress.Wpf.ExpressionEditor/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.DataAccess": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.RichEdit.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.RichEdit": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.ExpressionEditor.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.ExpressionEditor.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Grid/24.2.3": {"type": "package", "dependencies": {"DevExpress.Wpf.ExpressionEditor": "[24.2.3]", "DevExpress.Wpf.Grid.Printing": "[24.2.3]"}}, "DevExpress.Wpf.Grid.Core/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.dll": {"related": ".Core.xml;.Extensions.xml;.xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.dll": {"related": ".xml"}, "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.dll": {"related": ".Core.xml;.Extensions.xml;.xml"}}}, "DevExpress.Wpf.Grid.Printing/24.2.3": {"type": "package", "dependencies": {"DevExpress.Wpf.Grid.Core": "[24.2.3]", "DevExpress.Wpf.Printing": "[24.2.3]"}}, "DevExpress.Wpf.LayoutControl/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Office/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Office.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Office.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Office.v24.2.dll": {}}}, "DevExpress.Wpf.Printing/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Office.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.RichEdit.Core": "[24.2.3]", "DevExpress.Wpf.Controls": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.Docking": "[24.2.3]", "DevExpress.Wpf.DocumentViewer.Core": "[24.2.3]", "DevExpress.Wpf.Grid.Core": "[24.2.3]", "DevExpress.Wpf.LayoutControl": "[24.2.3]", "DevExpress.Wpf.Ribbon": "[24.2.3]", "System.ServiceModel.Http": "8.0.0"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.PropertyGrid/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.PropertyGrid.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.PropertyGrid.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Ribbon/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.RichEdit/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "DevExpress.Data.Desktop": "[24.2.3]", "DevExpress.Drawing": "[24.2.3]", "DevExpress.Images": "[24.2.3]", "DevExpress.Mvvm": "[24.2.3]", "DevExpress.Office.Core": "[24.2.3]", "DevExpress.Pdf.Core": "[24.2.3]", "DevExpress.Printing.Core": "[24.2.3]", "DevExpress.RichEdit.Core": "[24.2.3]", "DevExpress.Wpf.Core": "[24.2.3]", "DevExpress.Wpf.Docking": "[24.2.3]", "DevExpress.Wpf.Office": "[24.2.3]", "DevExpress.Wpf.Printing": "[24.2.3]", "DevExpress.Wpf.Ribbon": "[24.2.3]"}, "compile": {"lib/net8.0-windows/DevExpress.Xpf.RichEdit.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.RichEdit.v24.2.dll": {"related": ".xml"}}}, "DevExpress.Wpf.Themes.All/24.2.3": {"type": "package", "dependencies": {"DevExpress.Wpf.Themes.DXStyle": "[24.2.3]", "DevExpress.Wpf.Themes.LightGray": "[24.2.3]", "DevExpress.Wpf.Themes.MetropolisDark": "[24.2.3]", "DevExpress.Wpf.Themes.MetropolisLight": "[24.2.3]", "DevExpress.Wpf.Themes.Office2007Black": "[24.2.3]", "DevExpress.Wpf.Themes.Office2007Blue": "[24.2.3]", "DevExpress.Wpf.Themes.Office2007Silver": "[24.2.3]", "DevExpress.Wpf.Themes.Office2010Black": "[24.2.3]", "DevExpress.Wpf.Themes.Office2010Blue": "[24.2.3]", "DevExpress.Wpf.Themes.Office2010Silver": "[24.2.3]", "DevExpress.Wpf.Themes.Office2013": "[24.2.3]", "DevExpress.Wpf.Themes.Office2013DarkGray": "[24.2.3]", "DevExpress.Wpf.Themes.Office2013LightGray": "[24.2.3]", "DevExpress.Wpf.Themes.Office2016Black": "[24.2.3]", "DevExpress.Wpf.Themes.Office2016BlackSE": "[24.2.3]", "DevExpress.Wpf.Themes.Office2016Colorful": "[24.2.3]", "DevExpress.Wpf.Themes.Office2016ColorfulSE": "[24.2.3]", "DevExpress.Wpf.Themes.Office2016DarkGraySE": "[24.2.3]", "DevExpress.Wpf.Themes.Office2016White": "[24.2.3]", "DevExpress.Wpf.Themes.Office2016WhiteSE": "[24.2.3]", "DevExpress.Wpf.Themes.Office2019Black": "[24.2.3]", "DevExpress.Wpf.Themes.Office2019Colorful": "[24.2.3]", "DevExpress.Wpf.Themes.Office2019DarkGray": "[24.2.3]", "DevExpress.Wpf.Themes.Office2019HighContrast": "[24.2.3]", "DevExpress.Wpf.Themes.Office2019White": "[24.2.3]", "DevExpress.Wpf.Themes.Seven": "[24.2.3]", "DevExpress.Wpf.Themes.TouchlineDark": "[24.2.3]", "DevExpress.Wpf.Themes.VS2010": "[24.2.3]", "DevExpress.Wpf.Themes.VS2017Blue": "[24.2.3]", "DevExpress.Wpf.Themes.VS2017Dark": "[24.2.3]", "DevExpress.Wpf.Themes.VS2017Light": "[24.2.3]", "DevExpress.Wpf.Themes.VS2019Blue": "[24.2.3]", "DevExpress.Wpf.Themes.VS2019Dark": "[24.2.3]", "DevExpress.Wpf.Themes.VS2019Light": "[24.2.3]", "DevExpress.Wpf.Themes.Win10Dark": "[24.2.3]", "DevExpress.Wpf.Themes.Win10Light": "[24.2.3]", "DevExpress.Wpf.Themes.Win11Dark": "[24.2.3]", "DevExpress.Wpf.Themes.Win11Light": "[24.2.3]"}}, "DevExpress.Wpf.Themes.DXStyle/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.DXStyle.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.DXStyle.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.LightGray/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.LightGray.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.LightGray.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.MetropolisDark/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.MetropolisDark.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.MetropolisDark.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.MetropolisLight/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.MetropolisLight.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.MetropolisLight.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2007Black/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Black.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Black.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2007Blue/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Blue.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Blue.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2007Silver/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Silver.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Silver.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2010Black/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Black.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Black.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2010Blue/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Blue.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Blue.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2010Silver/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Silver.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Silver.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2013/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2013DarkGray/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013DarkGray.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013DarkGray.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2013LightGray/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013LightGray.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013LightGray.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2016Black/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016Black.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016Black.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2016BlackSE/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016BlackSE.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016BlackSE.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2016Colorful/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016Colorful.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016Colorful.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2016ColorfulSE/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016ColorfulSE.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016ColorfulSE.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2016DarkGraySE/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016DarkGraySE.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016DarkGraySE.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2016White/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016White.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016White.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2016WhiteSE/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016WhiteSE.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016WhiteSE.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2019Black/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Black.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Black.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2019Colorful/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2019DarkGray/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019DarkGray.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019DarkGray.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2019HighContrast/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019HighContrast.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019HighContrast.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Office2019White/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019White.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019White.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Seven/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Seven.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Seven.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.TouchlineDark/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.TouchlineDark.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.TouchlineDark.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.VS2010/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2010.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2010.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.VS2017Blue/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Blue.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Blue.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.VS2017Dark/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Dark.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Dark.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.VS2017Light/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Light.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Light.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.VS2019Blue/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Blue.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Blue.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.VS2019Dark/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Dark.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Dark.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.VS2019Light/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Light.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Light.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Win10Dark/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win10Dark.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win10Dark.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Win10Light/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win10Light.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win10Light.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Win11Dark/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win11Dark.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win11Dark.v24.2.dll": {}}}, "DevExpress.Wpf.Themes.Win11Light/24.2.3": {"type": "package", "compile": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win11Light.v24.2.dll": {}}, "runtime": {"lib/net8.0-windows/DevExpress.Xpf.Themes.Win11Light.v24.2.dll": {}}}, "DevExpress.Xpo/24.2.3": {"type": "package", "dependencies": {"DevExpress.Data": "[24.2.3]", "Microsoft.Extensions.DependencyInjection": "6.0.0", "System.Drawing.Common": "4.7.2", "System.Security.Cryptography.Pkcs": "8.0.1", "System.ServiceModel.Http": "6.2.0", "System.ServiceModel.NetTcp": "6.2.0"}, "compile": {"lib/net8.0/DevExpress.Xpo.v24.2.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/DevExpress.Xpo.v24.2.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Configuration/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"related": ".xml"}}, "build": {"buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets": {}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "System.Text.Json": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props": {}, "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets": {}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "9.0.0", "System.Text.Json": "9.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileSystemGlobbing": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.Configuration.CommandLine": "9.0.6", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.6", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Physical": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Logging.Console": "9.0.6", "Microsoft.Extensions.Logging.Debug": "9.0.6", "Microsoft.Extensions.Logging.EventLog": "9.0.6", "Microsoft.Extensions.Logging.EventSource": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "System.Diagnostics.DiagnosticSource": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Console/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Configuration": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Text.Json": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Diagnostics.EventLog": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "System.Text.Json": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.ObjectPool/6.0.16": {"type": "package", "compile": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.NETCore.Platforms/3.1.4": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}, "compile": {"ref/netstandard2.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"assetType": "runtime", "rid": "win"}}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"assetType": "native", "rid": "win-arm64"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"assetType": "native", "rid": "win-x64"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"assetType": "native", "rid": "win-x86"}}}, "Serilog/4.3.0": {"type": "package", "compile": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.dll": {"related": ".xml"}}, "build": {"build/Serilog.targets": {}}}, "Serilog.Enrichers.Environment/3.0.1": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Enrichers.Environment.dll": {"related": ".xml"}}}, "Serilog.Enrichers.Thread/4.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Enrichers.Thread.dll": {"related": ".xml"}}}, "Serilog.Extensions.Hosting/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Serilog": "4.2.0", "Serilog.Extensions.Logging": "9.0.0"}, "compile": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"related": ".xml"}}}, "Serilog.Extensions.Logging/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"related": ".xml"}}}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"related": ".xml"}}}, "Serilog.Sinks.Debug/3.0.0": {"type": "package", "dependencies": {"Serilog": "4.0.0"}, "compile": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.Debug.dll": {"related": ".xml"}}}, "Serilog.Sinks.File/7.0.0": {"type": "package", "dependencies": {"Serilog": "4.2.0"}, "compile": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"related": ".xml"}}}, "System.CodeDom/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Configuration.ConfigurationManager/8.0.1": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.1", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Data.OleDb/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1", "System.Diagnostics.PerformanceCounter": "8.0.1"}, "compile": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Data.OleDb.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Data.OleDb.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Data.SqlClient/4.8.6": {"type": "package", "dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "compile": {"ref/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/netcoreapp2.1/System.Data.SqlClient.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.DiagnosticSource/9.0.6": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.EventLog/9.0.6": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.PerformanceCounter/8.0.1": {"type": "package", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.1"}, "compile": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Drawing.Common/4.7.2": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "3.1.4", "Microsoft.Win32.SystemEvents": "4.7.0"}, "compile": {"ref/netcoreapp3.0/System.Drawing.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Drawing.Common.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Pipelines/9.0.6": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Security.AccessControl/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.1"}, "compile": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "compile": {"ref/netcoreapp3.0/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.ServiceModel.Http/8.0.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "8.0.0"}, "compile": {"lib/net8.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/System.ServiceModel.Http.dll": {"related": ".pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.Http.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.Http.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.Http.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.Http.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.Http.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.Http.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.Http.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.Http.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.Http.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.Http.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.Http.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.Http.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.Http.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetFramingBase/6.2.0": {"type": "package", "dependencies": {"System.ServiceModel.Primitives": "6.2.0"}, "compile": {"lib/net6.0/System.ServiceModel.NetFramingBase.dll": {}}, "runtime": {"lib/net6.0/System.ServiceModel.NetFramingBase.dll": {}}, "resource": {"lib/net6.0/cs/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.NetTcp/6.2.0": {"type": "package", "dependencies": {"System.ServiceModel.NetFramingBase": "6.2.0", "System.ServiceModel.Primitives": "6.2.0"}, "compile": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"related": ".pdb"}}, "resource": {"lib/net6.0/cs/System.ServiceModel.NetTcp.resources.dll": {"locale": "cs"}, "lib/net6.0/de/System.ServiceModel.NetTcp.resources.dll": {"locale": "de"}, "lib/net6.0/es/System.ServiceModel.NetTcp.resources.dll": {"locale": "es"}, "lib/net6.0/fr/System.ServiceModel.NetTcp.resources.dll": {"locale": "fr"}, "lib/net6.0/it/System.ServiceModel.NetTcp.resources.dll": {"locale": "it"}, "lib/net6.0/ja/System.ServiceModel.NetTcp.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/System.ServiceModel.NetTcp.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/System.ServiceModel.NetTcp.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/System.ServiceModel.NetTcp.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/System.ServiceModel.NetTcp.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/System.ServiceModel.NetTcp.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Primitives/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.ObjectPool": "6.0.16", "System.Security.Cryptography.Xml": "6.0.1"}, "compile": {"ref/net8.0/System.ServiceModel.Primitives.dll": {}}, "runtime": {"lib/net8.0/System.ServiceModel.Primitives.dll": {"related": ".pdb"}}, "resource": {"lib/net8.0/cs/System.ServiceModel.Primitives.resources.dll": {"locale": "cs"}, "lib/net8.0/de/System.ServiceModel.Primitives.resources.dll": {"locale": "de"}, "lib/net8.0/es/System.ServiceModel.Primitives.resources.dll": {"locale": "es"}, "lib/net8.0/fr/System.ServiceModel.Primitives.resources.dll": {"locale": "fr"}, "lib/net8.0/it/System.ServiceModel.Primitives.resources.dll": {"locale": "it"}, "lib/net8.0/ja/System.ServiceModel.Primitives.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/System.ServiceModel.Primitives.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/System.ServiceModel.Primitives.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/System.ServiceModel.Primitives.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/System.ServiceModel.Primitives.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/System.ServiceModel.Primitives.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/System.ServiceModel.Primitives.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Text.Encodings.Web/9.0.6": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/9.0.6": {"type": "package", "dependencies": {"System.IO.Pipelines": "9.0.6", "System.Text.Encodings.Web": "9.0.6"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}}}, "libraries": {"CommunityToolkit.Mvvm/8.4.0": {"sha512": "tqVU8yc/ADO9oiTRyTnwhFN68hCwvkliMierptWOudIAvWY1mWCh5VFh+guwHJmpMwfg0J0rY+yyd5Oy7ty9Uw==", "type": "package", "path": "communitytoolkit.mvvm/8.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "License.md", "ThirdPartyNotices.txt", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.0/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.12/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.CodeFixers.dll", "analyzers/dotnet/roslyn4.3/cs/CommunityToolkit.Mvvm.SourceGenerators.dll", "build/CommunityToolkit.Mvvm.FeatureSwitches.targets", "build/CommunityToolkit.Mvvm.SourceGenerators.targets", "build/CommunityToolkit.Mvvm.Windows.targets", "build/CommunityToolkit.Mvvm.WindowsSdk.targets", "build/CommunityToolkit.Mvvm.targets", "buildTransitive/CommunityToolkit.Mvvm.FeatureSwitches.targets", "buildTransitive/CommunityToolkit.Mvvm.SourceGenerators.targets", "buildTransitive/CommunityToolkit.Mvvm.Windows.targets", "buildTransitive/CommunityToolkit.Mvvm.WindowsSdk.targets", "buildTransitive/CommunityToolkit.Mvvm.targets", "communitytoolkit.mvvm.8.4.0.nupkg.sha512", "communitytoolkit.mvvm.nuspec", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.dll", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.pdb", "lib/net8.0-windows10.0.17763/CommunityToolkit.Mvvm.xml", "lib/net8.0/CommunityToolkit.Mvvm.dll", "lib/net8.0/CommunityToolkit.Mvvm.pdb", "lib/net8.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.0/CommunityToolkit.Mvvm.dll", "lib/netstandard2.0/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.0/CommunityToolkit.Mvvm.xml", "lib/netstandard2.1/CommunityToolkit.Mvvm.dll", "lib/netstandard2.1/CommunityToolkit.Mvvm.pdb", "lib/netstandard2.1/CommunityToolkit.Mvvm.xml"]}, "DevExpress.Charts.Core/24.2.3": {"sha512": "oRSobkT8FaNrKshJqP1XEcuvlPM2lZV9lyEWrLkVcueg49gqmjgTmSqHJesyTFJTcrpisRv/OhHe31eGdgqnVw==", "type": "package", "path": "devexpress.charts.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.charts.core.24.2.3.nupkg.sha512", "devexpress.charts.core.nuspec", "lib/net462/DevExpress.Charts.v24.2.Core.dll", "lib/net462/DevExpress.Charts.v24.2.Core.xml", "lib/net8.0/DevExpress.Charts.v24.2.Core.dll", "lib/net8.0/DevExpress.Charts.v24.2.Core.xml"]}, "DevExpress.CodeParser/24.2.3": {"sha512": "8beizsa5Cuf2BJG2KuQlpIchlqevVwMYh32JTPmaJWseuQEzLCEmdUcC+hPTnfLBw8DPEKo0Mn1tDC6+Koe5ew==", "type": "package", "path": "devexpress.codeparser/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.codeparser.24.2.3.nupkg.sha512", "devexpress.codeparser.nuspec", "lib/net462/DevExpress.CodeParser.v24.2.dll", "lib/net8.0/DevExpress.CodeParser.v24.2.dll"]}, "DevExpress.Data/24.2.3": {"sha512": "B35Z03dwKg5WCgmFNwz1gfOTubIsneMi+koh17iLHutKP1YpfvoR2hZNXDtuQqE7eL/HSuptTpwsvNtt+udQcw==", "type": "package", "path": "devexpress.data/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/DevExpress.Generator.dll", "devexpress.data.24.2.3.nupkg.sha512", "devexpress.data.nuspec", "lib/net462/DevExpress.Data.v24.2.dll", "lib/net462/DevExpress.Data.v24.2.xml", "lib/net8.0/DevExpress.Data.v24.2.dll", "lib/net8.0/DevExpress.Data.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Data.Desktop/24.2.3": {"sha512": "L9bDiEpPfSZhyjRdElmrbaVG8RbsVKFnuX76/jM0IcEr4PpeG7vsgvFUXdJuvb3NeXAOTVwJmqXyujrkJJKgNQ==", "type": "package", "path": "devexpress.data.desktop/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/DevExpress.Data.Desktop.props", "devexpress.data.desktop.24.2.3.nupkg.sha512", "devexpress.data.desktop.nuspec", "lib/net462/DevExpress.Data.Desktop.v24.2.dll", "lib/net462/DevExpress.Data.Desktop.v24.2.xml", "lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.dll", "lib/net8.0-windows/DevExpress.Data.Desktop.v24.2.xml"]}, "DevExpress.DataAccess/24.2.3": {"sha512": "PBL4GMIVxvYfp8GagTNZyviS8XonvYlpt1OV+Rv+h1xI10HKTLS119DspSW5PrcdA736qwF9/n6OCCCrdOKD5A==", "type": "package", "path": "devexpress.dataaccess/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.dataaccess.24.2.3.nupkg.sha512", "devexpress.dataaccess.nuspec", "lib/net462/DevExpress.DataAccess.v24.2.dll", "lib/net462/DevExpress.DataAccess.v24.2.xml", "lib/net8.0/DevExpress.DataAccess.v24.2.dll", "lib/net8.0/DevExpress.DataAccess.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.DataVisualization.Core/24.2.3": {"sha512": "4Xg+sLHCv0Jmc2soI9rZ5pxhp3RePtIJtGb0wqqYroF8gKgxU5umnb2Ia+N1ZVzcPdyNWviZfjuoKT/6b/EImA==", "type": "package", "path": "devexpress.datavisualization.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.datavisualization.core.24.2.3.nupkg.sha512", "devexpress.datavisualization.core.nuspec", "lib/net462/DevExpress.DataVisualization.v24.2.Core.dll", "lib/net8.0/DevExpress.DataVisualization.v24.2.Core.dll"]}, "DevExpress.Drawing/24.2.3": {"sha512": "ks/fg5GMeZnRKxghlnxw433IslBXJNzG5xRxEj8NMSVF7xCJFjbOdJ+rSeZ00Hca/faAEcKT+Cahx6xTAdsbRg==", "type": "package", "path": "devexpress.drawing/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.drawing.24.2.3.nupkg.sha512", "devexpress.drawing.nuspec", "lib/net462/DevExpress.Drawing.v24.2.dll", "lib/net462/DevExpress.Drawing.v24.2.xml", "lib/net8.0/DevExpress.Drawing.v24.2.dll", "lib/net8.0/DevExpress.Drawing.v24.2.xml"]}, "DevExpress.Images/24.2.3": {"sha512": "CCBfd0PI65GrgQzub6kHYsxA5CJwHq3zyOGllqv11ddp5vljy1FOOkrJkz80cBFTjiOm+PjOeVy7y+rbSs+Hfg==", "type": "package", "path": "devexpress.images/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.images.24.2.3.nupkg.sha512", "devexpress.images.nuspec", "lib/net462/DevExpress.Images.v24.2.dll", "lib/net8.0/DevExpress.Images.v24.2.dll"]}, "DevExpress.Mvvm/24.2.3": {"sha512": "M+Bm0Ts2rhv1rdtVVnujTdZOzg55rLzGzgc06JTnqx/0RbmhNaX4TUyifReMfFgz0ljqtbWBM0c6HxGrCaFTHA==", "type": "package", "path": "devexpress.mvvm/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.mvvm.24.2.3.nupkg.sha512", "devexpress.mvvm.nuspec", "lib/net462/DevExpress.Mvvm.v24.2.dll", "lib/net462/DevExpress.Mvvm.v24.2.xml", "lib/net8.0-windows/DevExpress.Mvvm.v24.2.dll", "lib/net8.0-windows/DevExpress.Mvvm.v24.2.xml"]}, "DevExpress.Office.Core/24.2.3": {"sha512": "Q5RKL6HmlvgpeWVaV/iA5VlADcmWGAN9V/P9DGeYdvYMBq2KVHeTZ3ReYSKqePd6dg4zqToMv1+sMW42Az03bw==", "type": "package", "path": "devexpress.office.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.office.core.24.2.3.nupkg.sha512", "devexpress.office.core.nuspec", "lib/net462/DevExpress.Office.v24.2.Core.dll", "lib/net462/DevExpress.Office.v24.2.Core.xml", "lib/net8.0/DevExpress.Office.v24.2.Core.dll", "lib/net8.0/DevExpress.Office.v24.2.Core.xml"]}, "DevExpress.Pdf.Core/24.2.3": {"sha512": "OT7SVpBhurLC1igYcbq6HP1QsSfV9HosrcBh18AkxYLCiQlzTbCMqApE8iE4vVS9re73ikWV/5g5guEYODPj9Q==", "type": "package", "path": "devexpress.pdf.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.core.24.2.3.nupkg.sha512", "devexpress.pdf.core.nuspec", "lib/net462/DevExpress.Pdf.v24.2.Core.dll", "lib/net462/DevExpress.Pdf.v24.2.Core.xml", "lib/net8.0/DevExpress.Pdf.v24.2.Core.dll", "lib/net8.0/DevExpress.Pdf.v24.2.Core.xml"]}, "DevExpress.Pdf.Drawing/24.2.3": {"sha512": "fHg5SzHeEXvcmM0hrfWzUNEy9WIshPc6TPi3U8GwvPWdS24RargWxSdtrVzeH14+YDKgFbHPA/o2vlYqdS+PeQ==", "type": "package", "path": "devexpress.pdf.drawing/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.pdf.drawing.24.2.3.nupkg.sha512", "devexpress.pdf.drawing.nuspec", "lib/net462/DevExpress.Pdf.v24.2.Drawing.dll", "lib/net462/DevExpress.Pdf.v24.2.Drawing.xml", "lib/net8.0/DevExpress.Pdf.v24.2.Drawing.dll", "lib/net8.0/DevExpress.Pdf.v24.2.Drawing.xml"]}, "DevExpress.Printing.Core/24.2.3": {"sha512": "/EB0Wr1ag7zdi7JmIQPHRXSb2pmj7X8DW2/S72x7u4nkChfTpMFSCZrYOw6EnJ1l8IbZHyeFpsWFiKgJtCPRUA==", "type": "package", "path": "devexpress.printing.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.printing.core.24.2.3.nupkg.sha512", "devexpress.printing.core.nuspec", "lib/net462/DevExpress.Printing.v24.2.Core.dll", "lib/net462/DevExpress.Printing.v24.2.Core.xml", "lib/net8.0/DevExpress.Printing.v24.2.Core.dll", "lib/net8.0/DevExpress.Printing.v24.2.Core.xml"]}, "DevExpress.RichEdit.Core/24.2.3": {"sha512": "lj2qJVSkEj+zW4G6gUCFzIo6TSIoN5+o6+oe0UIvlCMeb0rx3X/mDaQg4TXcK/y3FXQZk+DI+BY4mEzaFkXHwg==", "type": "package", "path": "devexpress.richedit.core/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.richedit.core.24.2.3.nupkg.sha512", "devexpress.richedit.core.nuspec", "lib/net462/DevExpress.RichEdit.v24.2.Core.dll", "lib/net462/DevExpress.RichEdit.v24.2.Core.xml", "lib/net8.0/DevExpress.RichEdit.v24.2.Core.dll", "lib/net8.0/DevExpress.RichEdit.v24.2.Core.xml"]}, "DevExpress.Wpf.Charts/24.2.3": {"sha512": "whtzCK/oGm6xK5DQBqGmgulUbOLzrr3l40G+WfaexkGDwzKzRO63yfj4iboxSPy+4zLkm0rb0K1bqllwe6H91g==", "type": "package", "path": "devexpress.wpf.charts/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.charts.24.2.3.nupkg.sha512", "devexpress.wpf.charts.nuspec", "lib/net462/Design/DevExpress.Xpf.Charts.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Charts.v24.2.DesignTools.dll", "lib/net462/DevExpress.Charts.Designer.v24.2.dll", "lib/net462/DevExpress.Charts.Designer.v24.2.xml", "lib/net462/DevExpress.Xpf.Charts.v24.2.dll", "lib/net462/DevExpress.Xpf.Charts.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Charts.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Charts.Designer.v24.2.dll", "lib/net8.0-windows/DevExpress.Charts.Designer.v24.2.xml", "lib/net8.0-windows/DevExpress.Xpf.Charts.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Charts.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Controls/24.2.3": {"sha512": "6+6IV55404/1ATYQfzbM5xAdTpCxxLz/GbHe6MG42vPh+vkeOCskbB4Fa3XLLv9KeMf2WMIBDLKPy1mYHTRZ7w==", "type": "package", "path": "devexpress.wpf.controls/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.controls.24.2.3.nupkg.sha512", "devexpress.wpf.controls.nuspec", "lib/net462/Design/DevExpress.Xpf.Controls.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Controls.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Controls.v24.2.dll", "lib/net462/DevExpress.Xpf.Controls.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Controls.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Controls.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Core/24.2.3": {"sha512": "VavHwmSoSYKFty33jEYi4TGFeZJnMG8jjU6yWe6v32zQOKniTlHxG/J1Pnhi+U1ukQN5gppGlVHxxu8vGjvMsg==", "type": "package", "path": "devexpress.wpf.core/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/net8.0-windows/DevExpress.Wpf.Core.props", "devexpress.wpf.core.24.2.3.nupkg.sha512", "devexpress.wpf.core.nuspec", "lib/net462/Design/DevExpress.Design.v24.2.dll", "lib/net462/Design/DevExpress.Images.v24.2.dll", "lib/net462/Design/DevExpress.Utils.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.VS2019.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.v24.2.Base.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.Design.Wizards.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll", "lib/net462/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll.config", "lib/net462/Design/DevExpress.Xpf.Themes.VS2019Blue.v24.2.dll", "lib/net462/Design/DevExpress.Xpf.Themes.VS2019Dark.v24.2.dll", "lib/net462/Design/DevExpress.Xpf.Themes.VS2019Light.v24.2.dll", "lib/net462/Design/Server/DesignerRunner.exe", "lib/net462/DevExpress.Xpf.Core.v24.2.dll", "lib/net462/DevExpress.Xpf.Core.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll", "lib/net8.0-windows/Design/DevExpress.Xpf.Core.v24.2.DesignTools.dll.config", "lib/net8.0-windows/Design/Server/DesignerRunner.dll", "lib/net8.0-windows/Design/Server/DesignerRunner.exe", "lib/net8.0-windows/Design/Server/DesignerRunner.runtimeconfig.json", "lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Core.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Docking/24.2.3": {"sha512": "cTKOOaIv94OmrB8lsfLIBIdmeT6XrDdJ/RvsiTkGAlfVZIJv5Qm9pFvURud8JnQ25uADX3WpEiX5dTi3nD+pqQ==", "type": "package", "path": "devexpress.wpf.docking/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.docking.24.2.3.nupkg.sha512", "devexpress.wpf.docking.nuspec", "lib/net462/Design/DevExpress.Xpf.Docking.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Docking.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Docking.v24.2.dll", "lib/net462/DevExpress.Xpf.Docking.v24.2.xml", "lib/net462/DevExpress.Xpf.Layout.v24.2.Core.dll", "lib/net462/DevExpress.Xpf.Layout.v24.2.Core.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Docking.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Docking.v24.2.xml", "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.dll", "lib/net8.0-windows/DevExpress.Xpf.Layout.v24.2.Core.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.DocumentViewer.Core/24.2.3": {"sha512": "MZioOx8xuYJMA+ryutZcx7HiWlJTrhVkV9wBD2IK/Gc512HSO3sILgMtgF4x2iD396u82b9aIzOH9GHzucssSQ==", "type": "package", "path": "devexpress.wpf.documentviewer.core/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.documentviewer.core.24.2.3.nupkg.sha512", "devexpress.wpf.documentviewer.core.nuspec", "lib/net462/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll", "lib/net462/DevExpress.Xpf.DocumentViewer.v24.2.Core.xml", "lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.dll", "lib/net8.0-windows/DevExpress.Xpf.DocumentViewer.v24.2.Core.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.ExpressionEditor/24.2.3": {"sha512": "E70X0sIrqZhhLq/0Q1Gk40sbEfSREmw5zX8UwdJJLdSn+bGnSO8LLyvP2e153aGkC+Ays/K01c28rDxsdq3E+w==", "type": "package", "path": "devexpress.wpf.expressioneditor/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.expressioneditor.24.2.3.nupkg.sha512", "devexpress.wpf.expressioneditor.nuspec", "lib/net462/DevExpress.Xpf.ExpressionEditor.v24.2.dll", "lib/net462/DevExpress.Xpf.ExpressionEditor.v24.2.xml", "lib/net8.0-windows/DevExpress.Xpf.ExpressionEditor.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.ExpressionEditor.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Grid/24.2.3": {"sha512": "w2uwe/fyyYyT+ZLtiGh3O4QxUH5Qi+k0T8vHXWvqpsweuPuJ2hpvCyUP074gECqvM3PkRd3/++Vh6VMyTB5l4A==", "type": "package", "path": "devexpress.wpf.grid/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.grid.24.2.3.nupkg.sha512", "devexpress.wpf.grid.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Grid.Core/24.2.3": {"sha512": "XIQdM2L5jTvNWZ35jGrVgCY8Ze/bGhjD4iqS37ardxi5ztoF8/T5YwxYH1SZ42KlbKIRRLfzy5Mtfd9ABrn+Lg==", "type": "package", "path": "devexpress.wpf.grid.core/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.grid.core.24.2.3.nupkg.sha512", "devexpress.wpf.grid.core.nuspec", "lib/net462/Design/DevExpress.VisualStudioInterop.VS2019.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.VS2022.v24.2.dll", "lib/net462/Design/DevExpress.VisualStudioInterop.v24.2.Base.dll", "lib/net462/Design/DevExpress.Xpf.Grid.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Grid.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.Core.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.Core.xml", "lib/net462/DevExpress.Xpf.Grid.v24.2.Extensions.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.Extensions.xml", "lib/net462/DevExpress.Xpf.Grid.v24.2.dll", "lib/net462/DevExpress.Xpf.Grid.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.VisualStudioInterop.VS2022.v24.2.dll", "lib/net8.0-windows/Design/DevExpress.VisualStudioInterop.v24.2.Base.dll", "lib/net8.0-windows/Design/DevExpress.Xpf.Grid.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Core.xml", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.Extensions.xml", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Grid.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Grid.Printing/24.2.3": {"sha512": "nBXsPARe3MUEOPxBVBQF8iOVpuVzhhCq5Vs82tpEn6VYsbdh1aq52FqjuHf3FgjpF3cREdoqd7f2ZjjU4OCk4Q==", "type": "package", "path": "devexpress.wpf.grid.printing/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.grid.printing.24.2.3.nupkg.sha512", "devexpress.wpf.grid.printing.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.LayoutControl/24.2.3": {"sha512": "bo0T0ysMUB5IxNFHYGD/vRg+WPaXaX6oHQ5khn2Nr73PYAh4D1Ke/WHkGTfQk8CEF/EkF1EBLdPVwz0umFgCpg==", "type": "package", "path": "devexpress.wpf.layoutcontrol/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.layoutcontrol.24.2.3.nupkg.sha512", "devexpress.wpf.layoutcontrol.nuspec", "lib/net462/Design/DevExpress.Xpf.LayoutControl.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.LayoutControl.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.LayoutControl.v24.2.dll", "lib/net462/DevExpress.Xpf.LayoutControl.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.LayoutControl.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.LayoutControl.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Office/24.2.3": {"sha512": "lscQkqZN4L2eEEoloq374TX+c+9g6GzPl3IxsTahTO++8BZzxiAzqNEMX2uJuYOQxeZeOBu9tXl+aE6y7heQjg==", "type": "package", "path": "devexpress.wpf.office/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.office.24.2.3.nupkg.sha512", "devexpress.wpf.office.nuspec", "lib/net462/DevExpress.Xpf.Office.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Office.v24.2.dll"]}, "DevExpress.Wpf.Printing/24.2.3": {"sha512": "s1sUL55Gs7YazfR8bJT+6NyjFSYNfc7bDFZdeHmZEKZtfPlGfHAJ3syCkGnIkGoR2uwiY6eiWtHtIR1yYvXQJA==", "type": "package", "path": "devexpress.wpf.printing/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.printing.24.2.3.nupkg.sha512", "devexpress.wpf.printing.nuspec", "lib/net462/Design/DevExpress.Xpf.Printing.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Printing.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Printing.v24.2.dll", "lib/net462/DevExpress.Xpf.Printing.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Printing.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Printing.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.PropertyGrid/24.2.3": {"sha512": "4YOQLBBKZRlgFTC2ptmLAFi4v/S+R6D1Ic7yj1O2jxNOD6ZPOjK7c0qZThCEXLindZkh//Sl7vEcVK5+mnzE9Q==", "type": "package", "path": "devexpress.wpf.propertygrid/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.propertygrid.24.2.3.nupkg.sha512", "devexpress.wpf.propertygrid.nuspec", "lib/net462/Design/DevExpress.Xpf.PropertyGrid.v24.2.Design.dll", "lib/net462/DevExpress.Xpf.PropertyGrid.v24.2.dll", "lib/net462/DevExpress.Xpf.PropertyGrid.v24.2.xml", "lib/net8.0-windows/DevExpress.Xpf.PropertyGrid.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.PropertyGrid.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Ribbon/24.2.3": {"sha512": "ujkIw62fxVYebmPus+AQC0+8asTmN66KZr/nKFW2fTULU/yxRoOpQQPoHjka6rp/pJGLXBOc7dc57r6TWyP4sg==", "type": "package", "path": "devexpress.wpf.ribbon/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.ribbon.24.2.3.nupkg.sha512", "devexpress.wpf.ribbon.nuspec", "lib/net462/Design/DevExpress.Xpf.Ribbon.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.Ribbon.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.Ribbon.v24.2.dll", "lib/net462/DevExpress.Xpf.Ribbon.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.Ribbon.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Ribbon.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.RichEdit/24.2.3": {"sha512": "wELk9hgIV2H3WLUIwWcnrV+WJ0Szv3HtA3h7z9BfVMRadiIc/d6ewdRGV0QrUzq8nkozTMUu1+d18dg1rbUh5Q==", "type": "package", "path": "devexpress.wpf.richedit/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.richedit.24.2.3.nupkg.sha512", "devexpress.wpf.richedit.nuspec", "lib/net462/Design/DevExpress.Xpf.RichEdit.v24.2.Design.dll", "lib/net462/Design/DevExpress.Xpf.RichEdit.v24.2.DesignTools.dll", "lib/net462/DevExpress.Xpf.RichEdit.v24.2.dll", "lib/net462/DevExpress.Xpf.RichEdit.v24.2.xml", "lib/net8.0-windows/Design/DevExpress.Xpf.RichEdit.v24.2.DesignTools.dll", "lib/net8.0-windows/DevExpress.Xpf.RichEdit.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.RichEdit.v24.2.xml", "tools/VisualStudioToolsManifest.xml"]}, "DevExpress.Wpf.Themes.All/24.2.3": {"sha512": "JBiGfpqa5caPpJeQxHNjF619MHlZUJGUxnZBHuLWSFhXJKwC+ihURcZBehoYYmSMZcxU7ZO3RzY6TVtiiWem8A==", "type": "package", "path": "devexpress.wpf.themes.all/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.all.24.2.3.nupkg.sha512", "devexpress.wpf.themes.all.nuspec"]}, "DevExpress.Wpf.Themes.DXStyle/24.2.3": {"sha512": "giqsBk59LIFZJdAbmHL+WxkbkU1IpI3X8Ty/l+0Ze0HFsFA6xqZmGeekLIzIppwMYdogW9Cy7jP27WbV0FJ+Ug==", "type": "package", "path": "devexpress.wpf.themes.dxstyle/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.dxstyle.24.2.3.nupkg.sha512", "devexpress.wpf.themes.dxstyle.nuspec", "lib/net462/DevExpress.Xpf.Themes.DXStyle.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.DXStyle.v24.2.dll"]}, "DevExpress.Wpf.Themes.LightGray/24.2.3": {"sha512": "qp1mmGT47XvOjVjPy/jKP0lntAkcjiweqVkj0oyDcR7Nzcw307nmd7Pmb827S/u46lcFYBakmscSvIKqgk7QvQ==", "type": "package", "path": "devexpress.wpf.themes.lightgray/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.lightgray.24.2.3.nupkg.sha512", "devexpress.wpf.themes.lightgray.nuspec", "lib/net462/DevExpress.Xpf.Themes.LightGray.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.LightGray.v24.2.dll"]}, "DevExpress.Wpf.Themes.MetropolisDark/24.2.3": {"sha512": "BvvQ0f14kG8H6YaSqZlosNVh9PC8Y5Imja7VlvEYRtU+aZF5tK18RQtYCn6b+QFZsKtsYf4yFHY5fD75dkM+iw==", "type": "package", "path": "devexpress.wpf.themes.metropolisdark/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.metropolisdark.24.2.3.nupkg.sha512", "devexpress.wpf.themes.metropolisdark.nuspec", "lib/net462/DevExpress.Xpf.Themes.MetropolisDark.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.MetropolisDark.v24.2.dll"]}, "DevExpress.Wpf.Themes.MetropolisLight/24.2.3": {"sha512": "2Ll1ski4ztsVCT1EZgOIUq7aNoCG0khk1AjfAG7gf07BbAVSXSK11VPRxe6u5Y9cBwbfkYLpd0qj0KjqLswXag==", "type": "package", "path": "devexpress.wpf.themes.metropolislight/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.metropolislight.24.2.3.nupkg.sha512", "devexpress.wpf.themes.metropolislight.nuspec", "lib/net462/DevExpress.Xpf.Themes.MetropolisLight.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.MetropolisLight.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2007Black/24.2.3": {"sha512": "kEai1EbjUpYUA5ZSBW+cP8+rwgTQ4TA65/31fydLB8yBEYIASXbMVlNi6HOJfh+huTb6CL6vXHI7TmNzmfUM8Q==", "type": "package", "path": "devexpress.wpf.themes.office2007black/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2007black.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2007black.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2007Black.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Black.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2007Blue/24.2.3": {"sha512": "jv8j7TIV0S0+zpZ1Vnb2L3/up+Q8xOy/DC8EXhcKe52DAj2sd3T7l+9Fiw9A1fPj8dY8LA75+WO6Jk3/RLDihg==", "type": "package", "path": "devexpress.wpf.themes.office2007blue/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2007blue.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2007blue.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2007Blue.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Blue.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2007Silver/24.2.3": {"sha512": "YH6ICdszdNxGyT1m+xJZj3ZtIfOpXzy2P04wchtd4KuhuSxeIH/XehdBzHaEDEXG6CRswTfbNzzRwKCc0kADEQ==", "type": "package", "path": "devexpress.wpf.themes.office2007silver/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2007silver.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2007silver.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2007Silver.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2007Silver.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2010Black/24.2.3": {"sha512": "CV8911VFG4YpJzU33v5ofoVMXYkqVL0S8KRkklO3HML/IT270hb6aIbsL9V6nCSelP61W4O2dTzHlB/Td77cjQ==", "type": "package", "path": "devexpress.wpf.themes.office2010black/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2010black.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2010black.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2010Black.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Black.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2010Blue/24.2.3": {"sha512": "ydK6F41czNhkcVCwWQs5uUKpDbcGn09F/x8b70MPNlLnAVx99+4tpnGzfalKo/LGoowyAtHSfj9FFa5BOruosw==", "type": "package", "path": "devexpress.wpf.themes.office2010blue/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2010blue.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2010blue.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2010Blue.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Blue.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2010Silver/24.2.3": {"sha512": "lI1rIkHdK/VT20dyB/nkGRB1QqbrjhpUd5XoOecL1fk9GNWhby6K17LotG3lLv796ioGIP2DsAiOiw5yUs7lqA==", "type": "package", "path": "devexpress.wpf.themes.office2010silver/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2010silver.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2010silver.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2010Silver.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2010Silver.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2013/24.2.3": {"sha512": "nxEZmAYYE7uwjE15FcVSnIq+xpMVjT/z2ZJl9j3LjsRYgvdQjYBq5I6Nh3tQhE8GYvYqD1WVduQGhyYC7Zj0Lg==", "type": "package", "path": "devexpress.wpf.themes.office2013/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2013.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2013.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2013.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2013DarkGray/24.2.3": {"sha512": "8zGNZnpFyrtW9dqzTVXPvzPvwzJg3C+9ybNwE/KBTrxAcJL2JllnZc6duP3JwF0pHrJGfFWY+8fpeCByZDdhuw==", "type": "package", "path": "devexpress.wpf.themes.office2013darkgray/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2013darkgray.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2013darkgray.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2013DarkGray.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013DarkGray.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2013LightGray/24.2.3": {"sha512": "YejLkWvhY8egYrHpc0FhIJJpE4PRhBTJG59nnI7jHTbo15SG42QAGB8q6NIgIPtid6gEwFS7YmragY5sRBPfbw==", "type": "package", "path": "devexpress.wpf.themes.office2013lightgray/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2013lightgray.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2013lightgray.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2013LightGray.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2013LightGray.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2016Black/24.2.3": {"sha512": "UaU8ro9eTU3Yb5IX7ET7wDuIxr5daZ7Dgl61ZT8nY4X4WIOpYZ0GrHScseDf8Zm8kWbydR7tbse2XzZs72wp6Q==", "type": "package", "path": "devexpress.wpf.themes.office2016black/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2016black.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2016black.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2016Black.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016Black.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2016BlackSE/24.2.3": {"sha512": "fw9tugEVz4KGFsXZRj/3B3WZ0n9w3ZFgSC01cS+fNwGihCx6hWgmI6kcVtWD4Ei9dlilS7kaCLFBYj4xXHJkOA==", "type": "package", "path": "devexpress.wpf.themes.office2016blackse/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2016blackse.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2016blackse.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2016BlackSE.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016BlackSE.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2016Colorful/24.2.3": {"sha512": "vpMccufNnzl+NDzrkNI9H1NL2hCz1HhiUVGuVtq4B56egik0vfG9dGf3ldQSCWKqmFdstKug4qtDEGNXxmFxaA==", "type": "package", "path": "devexpress.wpf.themes.office2016colorful/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2016colorful.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2016colorful.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2016Colorful.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016Colorful.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2016ColorfulSE/24.2.3": {"sha512": "aA/oOlhyxZaVHi4tCsUUq1nBtsbUpBQcmbyR27pd3Es3DLC0KaiF0LodWhT63FlYp4In5uFyblIvSAxeUHUA6g==", "type": "package", "path": "devexpress.wpf.themes.office2016colorfulse/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2016colorfulse.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2016colorfulse.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2016ColorfulSE.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016ColorfulSE.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2016DarkGraySE/24.2.3": {"sha512": "gc0bfFkMJ0vifk5PsYWSc+JcWfTLWQAJhGQixUXvEs562U1XDlme4vi5LOOomXEHOyjM8CybJE6skhskCPu/ew==", "type": "package", "path": "devexpress.wpf.themes.office2016darkgrayse/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2016darkgrayse.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2016darkgrayse.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2016DarkGraySE.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016DarkGraySE.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2016White/24.2.3": {"sha512": "bb80KApetVNvyGMvz5jnAqWVMrKBdrin3reUS3a4Nig0LOcKpSkD0hNtV+wsxsbMbkYFPxVTU2CmqjbIhwCHKw==", "type": "package", "path": "devexpress.wpf.themes.office2016white/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2016white.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2016white.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2016White.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016White.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2016WhiteSE/24.2.3": {"sha512": "CPnrv7a7tp5MweMI4eSwTgzmZ/M7EHWqh8vImX7FS4MbwXKd1xTYBpPKEr+jxZOfRwWcv95lV9bouGBatyGe8A==", "type": "package", "path": "devexpress.wpf.themes.office2016whitese/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2016whitese.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2016whitese.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2016WhiteSE.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2016WhiteSE.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2019Black/24.2.3": {"sha512": "0giGKya4ZWwAfXS1gM5EnpIDkuRjjL5uNceasY0xhbBor6zJ7KjZoXroj7E6efT2xRIhDxIvXQH3daqD8VNzYQ==", "type": "package", "path": "devexpress.wpf.themes.office2019black/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2019black.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2019black.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2019Black.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Black.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2019Colorful/24.2.3": {"sha512": "c76LI6oCSZ0dBlBKg8ysidzxqPEa1m9BAsdYMCT8oneYSrnnenFrDZ5qU9FdPw8XOhCsUvlSc8+JJOxprxEPuw==", "type": "package", "path": "devexpress.wpf.themes.office2019colorful/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2019colorful.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2019colorful.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019Colorful.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2019DarkGray/24.2.3": {"sha512": "NjYPVEkBrvDoz/P4M/J+iL3S7qwKAFaMDnTUO5NOHkzV6GSFsKrcgNweahnj68/MnUnaxnAMgNeNbhQgv3gXyw==", "type": "package", "path": "devexpress.wpf.themes.office2019darkgray/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2019darkgray.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2019darkgray.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2019DarkGray.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019DarkGray.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2019HighContrast/24.2.3": {"sha512": "7rwBbA14Z5Ux6QQdcXF0vG6DpsJ65QoscwQIgMS2v8h3CQJuCchA8uCPHmlwzxrJfE6h+0v4lytKbzHAlEsV4Q==", "type": "package", "path": "devexpress.wpf.themes.office2019highcontrast/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2019highcontrast.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2019highcontrast.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2019HighContrast.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019HighContrast.v24.2.dll"]}, "DevExpress.Wpf.Themes.Office2019White/24.2.3": {"sha512": "3lfdc648ZdoQtFoiPTbWP2y9DrIJHM/aIyHkisqxBtger5OJWKM4lz5fxTmpJx7euEfWNCRtpOlVpMzOtN5uYQ==", "type": "package", "path": "devexpress.wpf.themes.office2019white/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.office2019white.24.2.3.nupkg.sha512", "devexpress.wpf.themes.office2019white.nuspec", "lib/net462/DevExpress.Xpf.Themes.Office2019White.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Office2019White.v24.2.dll"]}, "DevExpress.Wpf.Themes.Seven/24.2.3": {"sha512": "DUby6hbm7x/absWDvXlODwno5eLdkR05BtasBb03fSWX3ugm3zbSpqUzIMgU6MqlNeHjynrDTf6gea5OKTF9ZQ==", "type": "package", "path": "devexpress.wpf.themes.seven/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.seven.24.2.3.nupkg.sha512", "devexpress.wpf.themes.seven.nuspec", "lib/net462/DevExpress.Xpf.Themes.Seven.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Seven.v24.2.dll"]}, "DevExpress.Wpf.Themes.TouchlineDark/24.2.3": {"sha512": "I+1o0Mj9d99lX858jkxc7xcQxaD0Eq0beNMTn0f9wb77V0YPJXGPBc3cSXIIvxI1K6pL0cyJixEb4uWN8k3AhQ==", "type": "package", "path": "devexpress.wpf.themes.touchlinedark/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.touchlinedark.24.2.3.nupkg.sha512", "devexpress.wpf.themes.touchlinedark.nuspec", "lib/net462/DevExpress.Xpf.Themes.TouchlineDark.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.TouchlineDark.v24.2.dll"]}, "DevExpress.Wpf.Themes.VS2010/24.2.3": {"sha512": "JmbLuTVMU6bTddikLPRHri3utb0jrO6JXoY/twEsgDVWnemN1/ph40w9wSPHFpVEI4ODcZ+H6s8v33gQRxLCXg==", "type": "package", "path": "devexpress.wpf.themes.vs2010/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.vs2010.24.2.3.nupkg.sha512", "devexpress.wpf.themes.vs2010.nuspec", "lib/net462/DevExpress.Xpf.Themes.VS2010.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.VS2010.v24.2.dll"]}, "DevExpress.Wpf.Themes.VS2017Blue/24.2.3": {"sha512": "hU8q1ZOaoyBkAPrhJaj8EZn79hQABWIfO+iK7qy19EFG1/W4ahx9PUU9IedWiSUOkDdGbD+ta5uvWoc6fMccKQ==", "type": "package", "path": "devexpress.wpf.themes.vs2017blue/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.vs2017blue.24.2.3.nupkg.sha512", "devexpress.wpf.themes.vs2017blue.nuspec", "lib/net462/DevExpress.Xpf.Themes.VS2017Blue.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Blue.v24.2.dll"]}, "DevExpress.Wpf.Themes.VS2017Dark/24.2.3": {"sha512": "u47sV7b2UwlbP9jaHef8wKNHklC4E90QEGDuK1Ki1bZbtTmAgJxQgMiA0YFSKY2SG/UcezG1F96hJKwgNjNPVw==", "type": "package", "path": "devexpress.wpf.themes.vs2017dark/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.vs2017dark.24.2.3.nupkg.sha512", "devexpress.wpf.themes.vs2017dark.nuspec", "lib/net462/DevExpress.Xpf.Themes.VS2017Dark.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Dark.v24.2.dll"]}, "DevExpress.Wpf.Themes.VS2017Light/24.2.3": {"sha512": "DF5fuWcacUhT8FQWPmJe6kllWah3ycDt1MQeu7j3iAlM2zD73bjmuNTYXKghnaVJGK8WbDaQs40tRoM18gip3w==", "type": "package", "path": "devexpress.wpf.themes.vs2017light/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.vs2017light.24.2.3.nupkg.sha512", "devexpress.wpf.themes.vs2017light.nuspec", "lib/net462/DevExpress.Xpf.Themes.VS2017Light.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.VS2017Light.v24.2.dll"]}, "DevExpress.Wpf.Themes.VS2019Blue/24.2.3": {"sha512": "j8gZ9PfExnX15Pxoy8XfPZkMxSyhuo5akNiRj19SMdw0sqXE6/6zirfaKUcqrC4TQTNHSbKmI1DvoF4VEcQkgw==", "type": "package", "path": "devexpress.wpf.themes.vs2019blue/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.vs2019blue.24.2.3.nupkg.sha512", "devexpress.wpf.themes.vs2019blue.nuspec", "lib/net462/DevExpress.Xpf.Themes.VS2019Blue.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Blue.v24.2.dll"]}, "DevExpress.Wpf.Themes.VS2019Dark/24.2.3": {"sha512": "WxoEWakic0UsTac/Uvmsxc+3KlZp4wle70T/1XTyIrV+dew8oQAE2Drq8vHCm2AjpkTl4PEKGNK7cmrbiH9ZzQ==", "type": "package", "path": "devexpress.wpf.themes.vs2019dark/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.vs2019dark.24.2.3.nupkg.sha512", "devexpress.wpf.themes.vs2019dark.nuspec", "lib/net462/DevExpress.Xpf.Themes.VS2019Dark.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Dark.v24.2.dll"]}, "DevExpress.Wpf.Themes.VS2019Light/24.2.3": {"sha512": "hTL5MaVgDIBvoOBbL7pGSPqjCDF9d4PyUmA77HtiKYOx2mvNelGFrC0wY+b8da/f6aL3hq7RmpdKfG84D3dEnw==", "type": "package", "path": "devexpress.wpf.themes.vs2019light/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.vs2019light.24.2.3.nupkg.sha512", "devexpress.wpf.themes.vs2019light.nuspec", "lib/net462/DevExpress.Xpf.Themes.VS2019Light.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.VS2019Light.v24.2.dll"]}, "DevExpress.Wpf.Themes.Win10Dark/24.2.3": {"sha512": "DseDaCYfRL7g92WaGq9m7Das1BHi0i4coI6wU33Jk/U5hiMQTsHNAFLPcIvrY7nWLnvT8M/pct+J9tP+ygwpQw==", "type": "package", "path": "devexpress.wpf.themes.win10dark/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.win10dark.24.2.3.nupkg.sha512", "devexpress.wpf.themes.win10dark.nuspec", "lib/net462/DevExpress.Xpf.Themes.Win10Dark.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Win10Dark.v24.2.dll"]}, "DevExpress.Wpf.Themes.Win10Light/24.2.3": {"sha512": "plG0Pa88evxTGvFk084iLlQ1Xtyej/mhi/EL8sHGHa65RLiJOxV6GLsda08uxGyVVZN254ts3rfp63hn3BQqOw==", "type": "package", "path": "devexpress.wpf.themes.win10light/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.win10light.24.2.3.nupkg.sha512", "devexpress.wpf.themes.win10light.nuspec", "lib/net462/DevExpress.Xpf.Themes.Win10Light.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Win10Light.v24.2.dll"]}, "DevExpress.Wpf.Themes.Win11Dark/24.2.3": {"sha512": "enQnm2KWXTG1YELr+rWGB9fOt6047nFY9O3i7I1BfJEPtWDBaNI2SompzE2a02BbwBmzqKYdjBm2jDBRiJ/25Q==", "type": "package", "path": "devexpress.wpf.themes.win11dark/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.win11dark.24.2.3.nupkg.sha512", "devexpress.wpf.themes.win11dark.nuspec", "lib/net462/DevExpress.Xpf.Themes.Win11Dark.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Win11Dark.v24.2.dll"]}, "DevExpress.Wpf.Themes.Win11Light/24.2.3": {"sha512": "eLWTMPYaErp/fa30xnMlPORYFjMVwhFKIfFrFu8m3azPzbKF8zYI3dsG257lj9YuaBgq+821aTvgKwqsZ3JfuA==", "type": "package", "path": "devexpress.wpf.themes.win11light/24.2.3", "files": [".nupkg.metadata", ".signature.p7s", "devexpress.wpf.themes.win11light.24.2.3.nupkg.sha512", "devexpress.wpf.themes.win11light.nuspec", "lib/net462/DevExpress.Xpf.Themes.Win11Light.v24.2.dll", "lib/net8.0-windows/DevExpress.Xpf.Themes.Win11Light.v24.2.dll"]}, "DevExpress.Xpo/24.2.3": {"sha512": "B+baNXaIr13vllwNTPzpG4IyGxwqjbOBDUS7eiVnD5mtJAh7jABO7oz8F0zKegfEvsMs0w+pwjibGv64n3Altg==", "type": "package", "path": "devexpress.xpo/24.2.3", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "devexpress.xpo.24.2.3.nupkg.sha512", "devexpress.xpo.nuspec", "lib/net462/DevExpress.Xpo.v24.2.dll", "lib/net462/DevExpress.Xpo.v24.2.xml", "lib/net8.0/DevExpress.Xpo.v24.2.dll", "lib/net8.0/DevExpress.Xpo.v24.2.xml", "readme.txt", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Extensions.Configuration/9.0.6": {"sha512": "VWB5jdkxHsRiuoniTqwOL32R4OWyp5If/bAucLjRJczRVNcwb8iCXKLjn3Inv8fv+jHMVMnvQLg7xhSys+y5PA==", "type": "package", "path": "microsoft.extensions.configuration/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"sha512": "3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Binder/9.0.6": {"sha512": "Opl/7SIrwDy9WjHn/vU2thQ8CUtrIWHLr+89I7/0VYNEJQvpL24zvqYrh83cH38RzNKHji0WGVkCVP6HJChVVw==", "type": "package", "path": "microsoft.extensions.configuration.binder/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.dll", "analyzers/dotnet/cs/cs/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/de/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/es/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/fr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/it/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ja/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ko/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pl/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/pt-BR/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/ru/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/tr/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-<PERSON>/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "analyzers/dotnet/cs/zh-Hant/Microsoft.Extensions.Configuration.Binder.SourceGeneration.resources.dll", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets", "lib/net462/Microsoft.Extensions.Configuration.Binder.dll", "lib/net462/Microsoft.Extensions.Configuration.Binder.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Binder.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.xml", "microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.binder.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.CommandLine/9.0.6": {"sha512": "DC5I4Y1nK35jY4piDqQCzWjDXzT6ECMctBAxgAJoc6pn0k6uyxcDeOuVDRooFui/N65ptn9xT5mk9eO4mSTj/g==", "type": "package", "path": "microsoft.extensions.configuration.commandline/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.CommandLine.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.CommandLine.targets", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net462/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/net9.0/Microsoft.Extensions.Configuration.CommandLine.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.xml", "microsoft.extensions.configuration.commandline.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.commandline.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.6": {"sha512": "RGYG2JBak9lf2rIPiZUVmWjUqoxaHPy3XPhPsJyIQ8QqK47rKvJz7jxVYefTnYdM5LTEiGFBdC7v3+SiosvmkQ==", "type": "package", "path": "microsoft.extensions.configuration.environmentvariables/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.targets", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net462/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net9.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "microsoft.extensions.configuration.environmentvariables.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.environmentvariables.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.6": {"sha512": "pCEueasI5JhJ24KYzMFxtG40zyLnWpcQYawpARh9FNq9XbWozuWgexmdkPa8p8YoVNlpi3ecKfcjfoRMkKAufw==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/9.0.6": {"sha512": "N0dgOYQ9tDzJouL9Tyx2dgMCcHV2pBaY8yVtorbDqYYwiDRS2zd1TbhTA2FMHqXF3SMjBoO+gONZcDoA79gdSA==", "type": "package", "path": "microsoft.extensions.configuration.json/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net9.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net9.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.6": {"sha512": "0ZZMzdvNwIS0f09S0IcaEbKFm+Xc41vRROsA/soeKEpzRISTDdiVwGlzdldbXEsuPjNVvNHyvIP8YW2hfIig0w==", "type": "package", "path": "microsoft.extensions.configuration.usersecrets/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net462/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/net8.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.props", "buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.targets", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net462/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/net9.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.xml", "microsoft.extensions.configuration.usersecrets.9.0.6.nupkg.sha512", "microsoft.extensions.configuration.usersecrets.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"sha512": "vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"sha512": "0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/9.0.0": {"sha512": "saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "type": "package", "path": "microsoft.extensions.dependencymodel/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net8.0/Microsoft.Extensions.DependencyModel.dll", "lib/net8.0/Microsoft.Extensions.DependencyModel.xml", "lib/net9.0/Microsoft.Extensions.DependencyModel.dll", "lib/net9.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics/9.0.6": {"sha512": "mIqCzZseDK9SqTRy4LxtjLwjlUu6aH5UdA6j0vgVER14yki9oRqLF+SmBiF6OlwsBSeL6dMQ8dmq02JMeE2puQ==", "type": "package", "path": "microsoft.extensions.diagnostics/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.targets", "lib/net462/Microsoft.Extensions.Diagnostics.dll", "lib/net462/Microsoft.Extensions.Diagnostics.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.xml", "microsoft.extensions.diagnostics.9.0.6.nupkg.sha512", "microsoft.extensions.diagnostics.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"sha512": "GIoXX7VDcTEsNM6yvffTBaOwnPQELGI5dzExR7L2O7AUkDsHBYIZawUbuwfq3cYzz8dIAAJotQYJMzH7qy27Ng==", "type": "package", "path": "microsoft.extensions.diagnostics.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Diagnostics.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Diagnostics.Abstractions.targets", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net462/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Diagnostics.Abstractions.xml", "microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.diagnostics.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.6": {"sha512": "q9FPkSGVA9ipI255p3PBAvWNXas5Tzjyp/DwYSwT+46mIFw9fWZahsF6vHpoxLt5/vtANotH2sAm7HunuFIx9g==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/9.0.6": {"sha512": "l+dFA0NRl90vSIiJNy5d7V0kpTEOWHTqbgoWYzlTwF5uiM5sWJ953haaELKE05jkyJdnemVTnqjrlgo4wo7oyg==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net9.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/9.0.6": {"sha512": "1HJCAbwukNEoYbHgHbKHmenU0V/0huw8+i7Qtf5rLUG1E+3kEwRJQxpwD3wbTEagIgPSQisNgJTvmUX9yYVc6g==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting/9.0.6": {"sha512": "Iu1UyXUnjMhoOwThKM0kCyjgWqqQnuujsbPMnF44ITUbmETT7RAVlozNgev2L/damwNoPZKpmwArRKBy2IOAZg==", "type": "package", "path": "microsoft.extensions.hosting/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.targets", "lib/net462/Microsoft.Extensions.Hosting.dll", "lib/net462/Microsoft.Extensions.Hosting.xml", "lib/net8.0/Microsoft.Extensions.Hosting.dll", "lib/net8.0/Microsoft.Extensions.Hosting.xml", "lib/net9.0/Microsoft.Extensions.Hosting.dll", "lib/net9.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.xml", "microsoft.extensions.hosting.9.0.6.nupkg.sha512", "microsoft.extensions.hosting.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Hosting.Abstractions/9.0.6": {"sha512": "G9T95JbcG/wQpeVIzg0IMwxI+uTywDmbxWUWN2P0mdna35rmuTqgTrZ4SU5rcfUT3EJfbI9N4K8UyCAAc6QK8Q==", "type": "package", "path": "microsoft.extensions.hosting.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Hosting.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Hosting.Abstractions.targets", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net462/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.Hosting.Abstractions.xml", "microsoft.extensions.hosting.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.hosting.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/9.0.6": {"sha512": "XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "type": "package", "path": "microsoft.extensions.logging/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/net9.0/Microsoft.Extensions.Logging.dll", "lib/net9.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.9.0.6.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"sha512": "LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Configuration/9.0.6": {"sha512": "lCgpxE5r6v43SB40/yUVnSWZUUqUZF5iUWizhkx4gqvq0L0rMw5g8adWKGO7sfIaSbCiU0et85sDQWswhLcceg==", "type": "package", "path": "microsoft.extensions.logging.configuration/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Configuration.targets", "lib/net462/Microsoft.Extensions.Logging.Configuration.dll", "lib/net462/Microsoft.Extensions.Logging.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/net9.0/Microsoft.Extensions.Logging.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.xml", "microsoft.extensions.logging.configuration.9.0.6.nupkg.sha512", "microsoft.extensions.logging.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Console/9.0.6": {"sha512": "L1O0M3MrqGlkrPYMLzcCphQpCG0lSHfTSPrm1otALNBzTPiO8rxxkjhBIIa2onKv92UP30Y4QaiigVMTx8YcxQ==", "type": "package", "path": "microsoft.extensions.logging.console/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Console.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Console.targets", "lib/net462/Microsoft.Extensions.Logging.Console.dll", "lib/net462/Microsoft.Extensions.Logging.Console.xml", "lib/net8.0/Microsoft.Extensions.Logging.Console.dll", "lib/net8.0/Microsoft.Extensions.Logging.Console.xml", "lib/net9.0/Microsoft.Extensions.Logging.Console.dll", "lib/net9.0/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Console.xml", "microsoft.extensions.logging.console.9.0.6.nupkg.sha512", "microsoft.extensions.logging.console.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Debug/9.0.6": {"sha512": "u21euQdOjaEwmlnnB1Zd4XGqOmWI8FkoGeUleV7n4BZ8HPQC/jrYzX/B5Cz3uI/FXjd//W88clPfkGIbSif7Jw==", "type": "package", "path": "microsoft.extensions.logging.debug/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.Debug.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Debug.targets", "lib/net462/Microsoft.Extensions.Logging.Debug.dll", "lib/net462/Microsoft.Extensions.Logging.Debug.xml", "lib/net8.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net8.0/Microsoft.Extensions.Logging.Debug.xml", "lib/net9.0/Microsoft.Extensions.Logging.Debug.dll", "lib/net9.0/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.xml", "microsoft.extensions.logging.debug.9.0.6.nupkg.sha512", "microsoft.extensions.logging.debug.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventLog/9.0.6": {"sha512": "IyyGy7xNJAjdlFYXc7SZ7kS3CWd3Ma4hing9QGtzXi+LXm8RWCEXdKA1cPx5AeFmdg3rVG+ADGIn44K14O+vFA==", "type": "package", "path": "microsoft.extensions.logging.eventlog/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventLog.targets", "lib/net462/Microsoft.Extensions.Logging.EventLog.dll", "lib/net462/Microsoft.Extensions.Logging.EventLog.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventLog.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventLog.xml", "microsoft.extensions.logging.eventlog.9.0.6.nupkg.sha512", "microsoft.extensions.logging.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.EventSource/9.0.6": {"sha512": "ayCRr/8ON3aINH81ak9l3vLAF/0pV/xrfChCbIlT2YnHAd4TYBWLcWhzbJWwPFV4XmJFrx/z8oq+gZzIc/74OA==", "type": "package", "path": "microsoft.extensions.logging.eventsource/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.EventSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.EventSource.targets", "lib/net462/Microsoft.Extensions.Logging.EventSource.dll", "lib/net462/Microsoft.Extensions.Logging.EventSource.xml", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net8.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/net9.0/Microsoft.Extensions.Logging.EventSource.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.xml", "microsoft.extensions.logging.eventsource.9.0.6.nupkg.sha512", "microsoft.extensions.logging.eventsource.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.ObjectPool/6.0.16": {"sha512": "OVX5tlKg6LY+XKqlUn7i9KY+6Liut0iewWff2DNr7129i/NJ8rpUzbmxavPydZgcLREEWHklXZiPKCS895tNIQ==", "type": "package", "path": "microsoft.extensions.objectpool/6.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.ObjectPool.dll", "lib/net461/Microsoft.Extensions.ObjectPool.xml", "lib/net6.0/Microsoft.Extensions.ObjectPool.dll", "lib/net6.0/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard2.0/Microsoft.Extensions.ObjectPool.xml", "microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "microsoft.extensions.objectpool.nuspec"]}, "Microsoft.Extensions.Options/9.0.6": {"sha512": "wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "type": "package", "path": "microsoft.extensions.options/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.6.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.6": {"sha512": "2lnp8nrvfzyp+5zvfeULm/hkZsDsKkl2ziBt5T8EZKoON5q+XRpRLoWcSPo8mP7GNZXpxKMBVjFNIZNbBIcnRw==", "type": "package", "path": "microsoft.extensions.options.configurationextensions/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.ConfigurationExtensions.targets", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net462/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.ConfigurationExtensions.xml", "microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512", "microsoft.extensions.options.configurationextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.6": {"sha512": "BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "type": "package", "path": "microsoft.extensions.primitives/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.6.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/3.1.4": {"sha512": "9/y05/CuxE+j184Nr4KihhB9KcUkvGojmD4JV4Vt/mHhVZR+eOCD5WCM+CXye9K0OFMsaPXbN+IcaIpjgBGZmg==", "type": "package", "path": "microsoft.netcore.platforms/3.1.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.3.1.4.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.Registry/4.7.0": {"sha512": "KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "type": "package", "path": "microsoft.win32.registry/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.4.7.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/net472/Microsoft.Win32.Registry.dll", "ref/net472/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/unix/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Win32.SystemEvents/4.7.0": {"sha512": "mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "type": "package", "path": "microsoft.win32.systemevents/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Win32.SystemEvents.dll", "lib/net461/Microsoft.Win32.SystemEvents.xml", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "lib/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "microsoft.win32.systemevents.4.7.0.nupkg.sha512", "microsoft.win32.systemevents.nuspec", "ref/net461/Microsoft.Win32.SystemEvents.dll", "ref/net461/Microsoft.Win32.SystemEvents.xml", "ref/net472/Microsoft.Win32.SystemEvents.dll", "ref/net472/Microsoft.Win32.SystemEvents.xml", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.dll", "ref/netstandard2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp2.0/Microsoft.Win32.SystemEvents.xml", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll", "runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.xml", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"sha512": "9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "type": "package", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "runtime.native.system.data.sqlclient.sni.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "type": "package", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-arm64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "type": "package", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x64.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x64/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"sha512": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "type": "package", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "runtime.win-x86.runtime.native.system.data.sqlclient.sni.nuspec", "runtimes/win-x86/native/sni.dll", "useSharedDesignerContext.txt", "version.txt"]}, "Serilog/4.3.0": {"sha512": "+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "type": "package", "path": "serilog/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "build/Serilog.targets", "icon.png", "lib/net462/Serilog.dll", "lib/net462/Serilog.xml", "lib/net471/Serilog.dll", "lib/net471/Serilog.xml", "lib/net6.0/Serilog.dll", "lib/net6.0/Serilog.xml", "lib/net8.0/Serilog.dll", "lib/net8.0/Serilog.xml", "lib/net9.0/Serilog.dll", "lib/net9.0/Serilog.xml", "lib/netstandard2.0/Serilog.dll", "lib/netstandard2.0/Serilog.xml", "serilog.4.3.0.nupkg.sha512", "serilog.nuspec"]}, "Serilog.Enrichers.Environment/3.0.1": {"sha512": "9BqCE4C9FF+/rJb/CsQwe7oVf44xqkOvMwX//CUxvUR25lFL4tSS6iuxE5eW07quby1BAyAEP+vM6TWsnT3iqw==", "type": "package", "path": "serilog.enrichers.environment/3.0.1", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Enrichers.Environment.dll", "lib/net462/Serilog.Enrichers.Environment.xml", "lib/net471/Serilog.Enrichers.Environment.dll", "lib/net471/Serilog.Enrichers.Environment.xml", "lib/net6.0/Serilog.Enrichers.Environment.dll", "lib/net6.0/Serilog.Enrichers.Environment.xml", "lib/net8.0/Serilog.Enrichers.Environment.dll", "lib/net8.0/Serilog.Enrichers.Environment.xml", "lib/netstandard2.0/Serilog.Enrichers.Environment.dll", "lib/netstandard2.0/Serilog.Enrichers.Environment.xml", "serilog-enricher-nuget.png", "serilog.enrichers.environment.3.0.1.nupkg.sha512", "serilog.enrichers.environment.nuspec"]}, "Serilog.Enrichers.Thread/4.0.0": {"sha512": "C7BK25a1rhUyr+Tp+1BYcVlBJq7M2VCHlIgnwoIUVJcicM9jYcvQK18+OeHiXw7uLPSjqWxJIp1EfaZ/RGmEwA==", "type": "package", "path": "serilog.enrichers.thread/4.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Enrichers.Thread.dll", "lib/net462/Serilog.Enrichers.Thread.xml", "lib/net471/Serilog.Enrichers.Thread.dll", "lib/net471/Serilog.Enrichers.Thread.xml", "lib/net6.0/Serilog.Enrichers.Thread.dll", "lib/net6.0/Serilog.Enrichers.Thread.xml", "lib/net8.0/Serilog.Enrichers.Thread.dll", "lib/net8.0/Serilog.Enrichers.Thread.xml", "lib/netstandard2.0/Serilog.Enrichers.Thread.dll", "lib/netstandard2.0/Serilog.Enrichers.Thread.xml", "serilog-enricher-nuget.png", "serilog.enrichers.thread.4.0.0.nupkg.sha512", "serilog.enrichers.thread.nuspec"]}, "Serilog.Extensions.Hosting/9.0.0": {"sha512": "u2TRxuxbjvTAldQn7uaAwePkWxTHIqlgjelekBtilAGL5sYyF3+65NWctN4UrwwGLsDC7c3Vz3HnOlu+PcoxXg==", "type": "package", "path": "serilog.extensions.hosting/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Extensions.Hosting.dll", "lib/net462/Serilog.Extensions.Hosting.xml", "lib/net8.0/Serilog.Extensions.Hosting.dll", "lib/net8.0/Serilog.Extensions.Hosting.xml", "lib/net9.0/Serilog.Extensions.Hosting.dll", "lib/net9.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.0/Serilog.Extensions.Hosting.dll", "lib/netstandard2.0/Serilog.Extensions.Hosting.xml", "lib/netstandard2.1/Serilog.Extensions.Hosting.dll", "lib/netstandard2.1/Serilog.Extensions.Hosting.xml", "serilog.extensions.hosting.9.0.0.nupkg.sha512", "serilog.extensions.hosting.nuspec"]}, "Serilog.Extensions.Logging/9.0.0": {"sha512": "NwSSYqPJeKNzl5AuXVHpGbr6PkZJFlNa14CdIebVjK3k/76kYj/mz5kiTRNVSsSaxM8kAIa1kpy/qyT9E4npRQ==", "type": "package", "path": "serilog.extensions.logging/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Extensions.Logging.dll", "lib/net462/Serilog.Extensions.Logging.xml", "lib/net8.0/Serilog.Extensions.Logging.dll", "lib/net8.0/Serilog.Extensions.Logging.xml", "lib/net9.0/Serilog.Extensions.Logging.dll", "lib/net9.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.0/Serilog.Extensions.Logging.dll", "lib/netstandard2.0/Serilog.Extensions.Logging.xml", "lib/netstandard2.1/Serilog.Extensions.Logging.dll", "lib/netstandard2.1/Serilog.Extensions.Logging.xml", "serilog-extension-nuget.png", "serilog.extensions.logging.9.0.0.nupkg.sha512", "serilog.extensions.logging.nuspec"]}, "Serilog.Settings.Configuration/9.0.0": {"sha512": "4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "type": "package", "path": "serilog.settings.configuration/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Settings.Configuration.dll", "lib/net462/Serilog.Settings.Configuration.xml", "lib/net8.0/Serilog.Settings.Configuration.dll", "lib/net8.0/Serilog.Settings.Configuration.xml", "lib/net9.0/Serilog.Settings.Configuration.dll", "lib/net9.0/Serilog.Settings.Configuration.xml", "lib/netstandard2.0/Serilog.Settings.Configuration.dll", "lib/netstandard2.0/Serilog.Settings.Configuration.xml", "serilog.settings.configuration.9.0.0.nupkg.sha512", "serilog.settings.configuration.nuspec"]}, "Serilog.Sinks.Debug/3.0.0": {"sha512": "4BzXcdrgRX7wde9PmHuYd9U6YqycCC28hhpKonK7hx0wb19eiuRj16fPcPSVp0o/Y1ipJuNLYQ00R3q2Zs8FDA==", "type": "package", "path": "serilog.sinks.debug/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "icon.png", "lib/net462/Serilog.Sinks.Debug.dll", "lib/net462/Serilog.Sinks.Debug.xml", "lib/net471/Serilog.Sinks.Debug.dll", "lib/net471/Serilog.Sinks.Debug.xml", "lib/net6.0/Serilog.Sinks.Debug.dll", "lib/net6.0/Serilog.Sinks.Debug.xml", "lib/net8.0/Serilog.Sinks.Debug.dll", "lib/net8.0/Serilog.Sinks.Debug.xml", "lib/netstandard2.0/Serilog.Sinks.Debug.dll", "lib/netstandard2.0/Serilog.Sinks.Debug.xml", "serilog.sinks.debug.3.0.0.nupkg.sha512", "serilog.sinks.debug.nuspec"]}, "Serilog.Sinks.File/7.0.0": {"sha512": "fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "type": "package", "path": "serilog.sinks.file/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Serilog.Sinks.File.dll", "lib/net462/Serilog.Sinks.File.xml", "lib/net471/Serilog.Sinks.File.dll", "lib/net471/Serilog.Sinks.File.xml", "lib/net6.0/Serilog.Sinks.File.dll", "lib/net6.0/Serilog.Sinks.File.xml", "lib/net8.0/Serilog.Sinks.File.dll", "lib/net8.0/Serilog.Sinks.File.xml", "lib/net9.0/Serilog.Sinks.File.dll", "lib/net9.0/Serilog.Sinks.File.xml", "lib/netstandard2.0/Serilog.Sinks.File.dll", "lib/netstandard2.0/Serilog.Sinks.File.xml", "serilog-sink-nuget.png", "serilog.sinks.file.7.0.0.nupkg.sha512", "serilog.sinks.file.nuspec"]}, "System.CodeDom/4.4.0": {"sha512": "2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "type": "package", "path": "system.codedom/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.dll", "ref/net461/System.CodeDom.dll", "ref/net461/System.CodeDom.xml", "ref/netstandard2.0/System.CodeDom.dll", "ref/netstandard2.0/System.CodeDom.xml", "system.codedom.4.4.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Configuration.ConfigurationManager/8.0.1": {"sha512": "gPYFPDyohW2gXNhdQRSjtmeS6FymL2crg4Sral1wtvEJ7DUqFCDWDVbbLobASbzxfic8U1hQEdC7hmg9LHncMw==", "type": "package", "path": "system.configuration.configurationmanager/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.1.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.OleDb/8.0.1": {"sha512": "RO+/y2ggU5956uQDRXdjA1e2l5yJ4rTWNX76eZ+3sgtYGqGapCe2kQCyiUci+/y6Fyb21Irp4RQEdfrIiuYrxQ==", "type": "package", "path": "system.data.oledb/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Data.OleDb.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Data.OleDb.targets", "lib/net462/System.Data.OleDb.dll", "lib/net462/System.Data.OleDb.xml", "lib/net6.0/System.Data.OleDb.dll", "lib/net6.0/System.Data.OleDb.xml", "lib/net7.0/System.Data.OleDb.dll", "lib/net7.0/System.Data.OleDb.xml", "lib/net8.0/System.Data.OleDb.dll", "lib/net8.0/System.Data.OleDb.xml", "lib/netstandard2.0/System.Data.OleDb.dll", "lib/netstandard2.0/System.Data.OleDb.xml", "runtimes/win/lib/net6.0/System.Data.OleDb.dll", "runtimes/win/lib/net6.0/System.Data.OleDb.xml", "runtimes/win/lib/net7.0/System.Data.OleDb.dll", "runtimes/win/lib/net7.0/System.Data.OleDb.xml", "runtimes/win/lib/net8.0/System.Data.OleDb.dll", "runtimes/win/lib/net8.0/System.Data.OleDb.xml", "system.data.oledb.8.0.1.nupkg.sha512", "system.data.oledb.nuspec", "useSharedDesignerContext.txt"]}, "System.Data.SqlClient/4.8.6": {"sha512": "2Ij/LCaTQRyAi5lAv7UUTV9R2FobC8xN9mE0fXBZohum/xLl8IZVmE98Rq5ugQHjCgTBRKqpXRb4ORulRdA6Ig==", "type": "package", "path": "system.data.sqlclient/4.8.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/System.Data.SqlClient.dll", "lib/net46/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.dll", "lib/net461/System.Data.SqlClient.xml", "lib/netcoreapp2.1/System.Data.SqlClient.dll", "lib/netcoreapp2.1/System.Data.SqlClient.xml", "lib/netstandard1.2/System.Data.SqlClient.dll", "lib/netstandard1.2/System.Data.SqlClient.xml", "lib/netstandard1.3/System.Data.SqlClient.dll", "lib/netstandard1.3/System.Data.SqlClient.xml", "lib/netstandard2.0/System.Data.SqlClient.dll", "lib/netstandard2.0/System.Data.SqlClient.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/System.Data.SqlClient.dll", "ref/net46/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.dll", "ref/net461/System.Data.SqlClient.xml", "ref/netcoreapp2.1/System.Data.SqlClient.dll", "ref/netcoreapp2.1/System.Data.SqlClient.xml", "ref/netstandard1.2/System.Data.SqlClient.dll", "ref/netstandard1.2/System.Data.SqlClient.xml", "ref/netstandard1.2/de/System.Data.SqlClient.xml", "ref/netstandard1.2/es/System.Data.SqlClient.xml", "ref/netstandard1.2/fr/System.Data.SqlClient.xml", "ref/netstandard1.2/it/System.Data.SqlClient.xml", "ref/netstandard1.2/ja/System.Data.SqlClient.xml", "ref/netstandard1.2/ko/System.Data.SqlClient.xml", "ref/netstandard1.2/ru/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.2/zh-hant/System.Data.SqlClient.xml", "ref/netstandard1.3/System.Data.SqlClient.dll", "ref/netstandard1.3/System.Data.SqlClient.xml", "ref/netstandard1.3/de/System.Data.SqlClient.xml", "ref/netstandard1.3/es/System.Data.SqlClient.xml", "ref/netstandard1.3/fr/System.Data.SqlClient.xml", "ref/netstandard1.3/it/System.Data.SqlClient.xml", "ref/netstandard1.3/ja/System.Data.SqlClient.xml", "ref/netstandard1.3/ko/System.Data.SqlClient.xml", "ref/netstandard1.3/ru/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hans/System.Data.SqlClient.xml", "ref/netstandard1.3/zh-hant/System.Data.SqlClient.xml", "ref/netstandard2.0/System.Data.SqlClient.dll", "ref/netstandard2.0/System.Data.SqlClient.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/unix/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/unix/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/net451/System.Data.SqlClient.dll", "runtimes/win/lib/net46/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.dll", "runtimes/win/lib/net461/System.Data.SqlClient.xml", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll", "runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.xml", "runtimes/win/lib/netstandard1.3/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll", "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.xml", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.dll", "runtimes/win/lib/uap10.0.16299/System.Data.SqlClient.xml", "system.data.sqlclient.4.8.6.nupkg.sha512", "system.data.sqlclient.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/9.0.6": {"sha512": "nikkwAKqpwWUvV5J8S9fnOPYg8k75Lf9fAI4bd6pyhyqNma0Py9kt+zcqXbe4TjJ4sTPcdYpPg81shYTrXnUZQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/net9.0/System.Diagnostics.DiagnosticSource.dll", "lib/net9.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/9.0.6": {"sha512": "lum+Dv+8S4gqN5H1C576UcQe0M2buoRjEUVs4TctXRSWjBH3ay3w2KyQrOo1yPdRs1I+xK69STz+4mjIisFI5w==", "type": "package", "path": "system.diagnostics.eventlog/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.6.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.PerformanceCounter/8.0.1": {"sha512": "9RfEDiEjlUADeThs8IPdDVTXSnPRSqjfgTQJALpmGFPKC0k2mbdufOXnb/9JZ4I0TkmxOfy3VTJxrHOJSs8cXg==", "type": "package", "path": "system.diagnostics.performancecounter/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.PerformanceCounter.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.PerformanceCounter.targets", "lib/net462/System.Diagnostics.PerformanceCounter.dll", "lib/net462/System.Diagnostics.PerformanceCounter.xml", "lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll", "lib/netstandard2.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.xml", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.dll", "runtimes/win/lib/net8.0/System.Diagnostics.PerformanceCounter.xml", "system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "system.diagnostics.performancecounter.nuspec", "useSharedDesignerContext.txt"]}, "System.Drawing.Common/4.7.2": {"sha512": "I2y4KBK3VCvU/WqE2xv7NjQ67maXHttkFSHYKgU2evrG9Yqh0oFjfORXt5hZTk+BVjdyFo2h0/YQZsca33BGmg==", "type": "package", "path": "system.drawing.common/4.7.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Drawing.Common.dll", "lib/netstandard2.0/System.Drawing.Common.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.dll", "ref/netcoreapp3.0/System.Drawing.Common.xml", "ref/netstandard2.0/System.Drawing.Common.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.xml", "runtimes/win/lib/netcoreapp2.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll", "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.xml", "system.drawing.common.4.7.2.nupkg.sha512", "system.drawing.common.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IO.Pipelines/9.0.6": {"sha512": "0nlr0reXrRmkZNKifKqh2DgGhQgfkT7Qa3gQxIn/JI7/y3WDiTz67M+Sq3vFhUqcG8O5zVrpqHvIHeGPGUBsEw==", "type": "package", "path": "system.io.pipelines/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.6.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.AccessControl/6.0.0": {"sha512": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "type": "package", "path": "system.security.accesscontrol/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/8.0.1": {"sha512": "CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/8.0.0": {"sha512": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "type": "package", "path": "system.security.cryptography.protecteddata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Xml/6.0.1": {"sha512": "5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "type": "package", "path": "system.security.cryptography.xml/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Xml.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.Cryptography.Xml.dll", "lib/net461/System.Security.Cryptography.Xml.xml", "lib/net6.0/System.Security.Cryptography.Xml.dll", "lib/net6.0/System.Security.Cryptography.Xml.xml", "lib/netstandard2.0/System.Security.Cryptography.Xml.dll", "lib/netstandard2.0/System.Security.Cryptography.Xml.xml", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Xml.xml", "system.security.cryptography.xml.6.0.1.nupkg.sha512", "system.security.cryptography.xml.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Principal.Windows/4.7.0": {"sha512": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "type": "package", "path": "system.security.principal.windows/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.4.7.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ServiceModel.Http/8.0.0": {"sha512": "Qwkoe0F+2e/2LiNwiIgfBTJTw11flv208UwS38ru+GR7nZk2VdGvAE8tqGB0RQIGra73Rux9jKNgfy1XtfXdLg==", "type": "package", "path": "system.servicemodel.http/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/System.ServiceModel.Http.dll", "lib/net8.0/System.ServiceModel.Http.pdb", "lib/net8.0/cs/System.ServiceModel.Http.resources.dll", "lib/net8.0/de/System.ServiceModel.Http.resources.dll", "lib/net8.0/es/System.ServiceModel.Http.resources.dll", "lib/net8.0/fr/System.ServiceModel.Http.resources.dll", "lib/net8.0/it/System.ServiceModel.Http.resources.dll", "lib/net8.0/ja/System.ServiceModel.Http.resources.dll", "lib/net8.0/ko/System.ServiceModel.Http.resources.dll", "lib/net8.0/pl/System.ServiceModel.Http.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.Http.resources.dll", "lib/net8.0/ru/System.ServiceModel.Http.resources.dll", "lib/net8.0/tr/System.ServiceModel.Http.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.Http.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.Http.resources.dll", "system.servicemodel.http.8.0.0.nupkg.sha512", "system.servicemodel.http.nuspec"]}, "System.ServiceModel.NetFramingBase/6.2.0": {"sha512": "204c9SNDKyQrDKv6F9MLlWKnM7UthRErFByJCHj8y9DtcgMAQnEB5xJvh+9ECmJgG13LJLOAMB5f3CjMatzz/A==", "type": "package", "path": "system.servicemodel.netframingbase/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.ServiceModel.NetFramingBase.dll", "lib/net6.0/cs/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/de/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/es/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/fr/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/it/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/ja/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/ko/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/pl/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/pt-BR/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/ru/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/tr/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/zh-Hans/System.ServiceModel.NetFramingBase.resources.dll", "lib/net6.0/zh-Hant/System.ServiceModel.NetFramingBase.resources.dll", "system.servicemodel.netframingbase.6.2.0.nupkg.sha512", "system.servicemodel.netframingbase.nuspec"]}, "System.ServiceModel.NetTcp/6.2.0": {"sha512": "FXTDhh8DgCfNyY5k9sNlqvhBVYqVM+0GZBsJfFMH5P5q7qGmTxql3bG9tae1Z+uMXJpG2jLbo1CfgusZ75lADA==", "type": "package", "path": "system.servicemodel.nettcp/6.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.ServiceModel.NetTcp.dll", "lib/net6.0/System.ServiceModel.NetTcp.pdb", "lib/net6.0/cs/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/de/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/es/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/fr/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/it/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/ja/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/ko/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/pl/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/pt-BR/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/ru/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/tr/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/zh-Hans/System.ServiceModel.NetTcp.resources.dll", "lib/net6.0/zh-Hant/System.ServiceModel.NetTcp.resources.dll", "system.servicemodel.nettcp.6.2.0.nupkg.sha512", "system.servicemodel.nettcp.nuspec"]}, "System.ServiceModel.Primitives/8.0.0": {"sha512": "hVzK77Bl00H+1V7ho7h03tKlgxAIKssV3eUnRdH+gTCZCK4Ywnv2CR35AV9ly/tRpvsGwNL1d/jkAwB1MWw3Fw==", "type": "package", "path": "system.servicemodel.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net8.0/System.ServiceModel.Primitives.dll", "lib/net8.0/System.ServiceModel.Primitives.pdb", "lib/net8.0/cs/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/de/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/es/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/fr/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/it/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ja/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ko/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/pl/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/pt-BR/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/ru/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/tr/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/zh-Hans/System.ServiceModel.Primitives.resources.dll", "lib/net8.0/zh-Hant/System.ServiceModel.Primitives.resources.dll", "ref/net8.0/System.ServiceModel.Primitives.dll", "system.servicemodel.primitives.8.0.0.nupkg.sha512", "system.servicemodel.primitives.nuspec"]}, "System.Text.Encodings.Web/9.0.6": {"sha512": "uWRgViw2yJAUyGxrzDLCc6fkzE2dZIoXxs8V6YjCujKsJuP0pnpYSlbm2/7tKd0SjBnMtwfDQhLenk3bXonVOA==", "type": "package", "path": "system.text.encodings.web/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/net9.0/System.Text.Encodings.Web.dll", "lib/net9.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.9.0.6.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.6": {"sha512": "h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "type": "package", "path": "system.text.json/9.0.6", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.6.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["CommunityToolkit.Mvvm >= 8.4.0", "DevExpress.Wpf.Charts >= 24.2.3", "DevExpress.Wpf.Controls >= 24.2.3", "DevExpress.Wpf.Core >= 24.2.3", "DevExpress.Wpf.Grid >= 24.2.3", "DevExpress.Wpf.Themes.All >= 24.2.3", "Microsoft.Extensions.Configuration >= 9.0.6", "Microsoft.Extensions.Configuration.Json >= 9.0.6", "Microsoft.Extensions.DependencyInjection >= 9.0.6", "Microsoft.Extensions.Hosting >= 9.0.6", "Serilog >= 4.3.0", "Serilog.Enrichers.Environment >= 3.0.1", "Serilog.Enrichers.Thread >= 4.0.0", "Serilog.Extensions.Hosting >= 9.0.0", "Serilog.Settings.Configuration >= 9.0.0", "Serilog.Sinks.Debug >= 3.0.0", "Serilog.Sinks.File >= 7.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\DevExpress 23.2\\Components\\Offline Packages": {}, "e:\\DevExpress 24.2\\Components\\Offline Packages": {}, "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}, "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\01 AirMonitor\\AirMonitor\\AirMonitor.csproj", "projectName": "AirMonitor", "projectPath": "D:\\01 AirMonitor\\AirMonitor\\AirMonitor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\01 AirMonitor\\AirMonitor\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.4.0, )"}, "DevExpress.Wpf.Charts": {"target": "Package", "version": "[24.2.3, )"}, "DevExpress.Wpf.Controls": {"target": "Package", "version": "[24.2.3, )"}, "DevExpress.Wpf.Core": {"target": "Package", "version": "[24.2.3, )"}, "DevExpress.Wpf.Grid": {"target": "Package", "version": "[24.2.3, )"}, "DevExpress.Wpf.Themes.All": {"target": "Package", "version": "[24.2.3, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Debug": {"target": "Package", "version": "[3.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}