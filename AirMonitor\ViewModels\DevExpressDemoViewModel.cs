using AirMonitor.Core;
using AirMonitor.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;

namespace AirMonitor.ViewModels;

/// <summary>
/// DevExpress演示页面ViewModel
/// </summary>
public partial class DevExpressDemoViewModel : BaseViewModel
{
    private readonly IDevExpressThemeService _themeService;
    private readonly IDialogService _dialogService;

    public DevExpressDemoViewModel(
        ILogger<DevExpressDemoViewModel> logger,
        IDevExpressThemeService themeService,
        IDialogService dialogService) : base(logger)
    {
        _themeService = themeService ?? throw new ArgumentNullException(nameof(themeService));
        _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
        
        Title = "DevExpress 控件演示";
        
        // 初始化数据
        InitializeData();
        
        // 监听主题变更
        _themeService.ThemeChanged += OnThemeChanged;
    }

    [ObservableProperty]
    private string _selectedTheme = string.Empty;

    [ObservableProperty]
    private ObservableCollection<SampleDataItem> _gridData = new();

    [ObservableProperty]
    private ObservableCollection<ChartDataItem> _chartData = new();

    [ObservableProperty]
    private SampleDataItem? _selectedGridItem;

    /// <summary>
    /// 可用主题列表
    /// </summary>
    public IEnumerable<ThemeItem> AvailableThemes => 
        _themeService.AvailableThemes.Select(theme => new ThemeItem
        {
            Name = theme,
            DisplayName = _themeService.GetThemeDisplayName(theme)
        });

    /// <summary>
    /// 当前主题名称
    /// </summary>
    public string CurrentThemeName => _themeService.CurrentThemeName;

    [RelayCommand]
    private async Task ChangeThemeAsync()
    {
        if (string.IsNullOrEmpty(SelectedTheme))
            return;

        await ExecuteAsync(async () =>
        {
            _themeService.ApplyTheme(SelectedTheme);
            await Task.Delay(100); // 给主题切换一点时间
            
            await _dialogService.ShowInfoAsync($"主题已切换为: {_themeService.GetThemeDisplayName(SelectedTheme)}", "主题切换");
        }, "正在切换主题...", "主题切换完成");
    }

    [RelayCommand]
    private async Task ShowGridDetailsAsync()
    {
        if (SelectedGridItem == null)
        {
            await _dialogService.ShowWarningAsync("请先选择一个数据项", "提示");
            return;
        }

        var message = $"ID: {SelectedGridItem.Id}\n" +
                     $"名称: {SelectedGridItem.Name}\n" +
                     $"值: {SelectedGridItem.Value:F2}\n" +
                     $"日期: {SelectedGridItem.Date:yyyy-MM-dd}\n" +
                     $"状态: {SelectedGridItem.Status}";

        await _dialogService.ShowInfoAsync(message, "数据详情");
    }

    [RelayCommand]
    private async Task AddDataItemAsync()
    {
        var name = await _dialogService.ShowInputAsync("请输入名称:", "添加数据", $"项目 {GridData.Count + 1}");
        if (string.IsNullOrWhiteSpace(name))
            return;

        await ExecuteAsync(async () =>
        {
            var random = new Random();
            var newItem = new SampleDataItem
            {
                Id = GridData.Count + 1,
                Name = name,
                Value = random.NextDouble() * 1000,
                Date = DateTime.Now.AddDays(-random.Next(0, 365)),
                Status = random.Next(0, 2) == 0 ? "活跃" : "非活跃"
            };

            GridData.Add(newItem);
            
            // 同时更新图表数据
            ChartData.Add(new ChartDataItem
            {
                Category = newItem.Name,
                Value = newItem.Value
            });

            await Task.CompletedTask;
        }, "正在添加数据...", "数据添加完成", true);
    }

    [RelayCommand]
    private async Task RemoveDataItemAsync()
    {
        if (SelectedGridItem == null)
        {
            await _dialogService.ShowWarningAsync("请先选择要删除的数据项", "提示");
            return;
        }

        var confirm = await _dialogService.ShowConfirmAsync($"确定要删除 '{SelectedGridItem.Name}' 吗？", "确认删除");
        if (!confirm)
            return;

        await ExecuteAsync(async () =>
        {
            GridData.Remove(SelectedGridItem);
            
            // 同时从图表数据中移除
            var chartItem = ChartData.FirstOrDefault(x => x.Category == SelectedGridItem.Name);
            if (chartItem != null)
                ChartData.Remove(chartItem);

            SelectedGridItem = null;
            await Task.CompletedTask;
        }, "正在删除数据...", "数据删除完成", true);
    }

    private void InitializeData()
    {
        // 初始化网格数据
        var random = new Random();
        var sampleData = new[]
        {
            "产品A", "产品B", "产品C", "产品D", "产品E",
            "服务X", "服务Y", "服务Z", "项目1", "项目2"
        };

        for (int i = 0; i < sampleData.Length; i++)
        {
            var item = new SampleDataItem
            {
                Id = i + 1,
                Name = sampleData[i],
                Value = random.NextDouble() * 1000,
                Date = DateTime.Now.AddDays(-random.Next(0, 365)),
                Status = random.Next(0, 2) == 0 ? "活跃" : "非活跃"
            };
            
            GridData.Add(item);
            
            // 同时添加到图表数据
            ChartData.Add(new ChartDataItem
            {
                Category = item.Name,
                Value = item.Value
            });
        }

        SelectedTheme = _themeService.CurrentThemeName;
    }

    private void OnThemeChanged(object? sender, string themeName)
    {
        OnPropertyChanged(nameof(CurrentThemeName));
        _logger.LogInformation("主题已变更为: {ThemeName}", themeName);
    }
}

/// <summary>
/// 示例数据项
/// </summary>
public class SampleDataItem
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public DateTime Date { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// 图表数据项
/// </summary>
public class ChartDataItem
{
    public string Category { get; set; } = string.Empty;
    public double Value { get; set; }
}

/// <summary>
/// 主题项
/// </summary>
public class ThemeItem
{
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
}
