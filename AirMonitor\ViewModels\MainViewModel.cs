using AirMonitor.Core;
using AirMonitor.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Windows.Threading;

namespace AirMonitor.ViewModels;

/// <summary>
/// 主窗口ViewModel
/// </summary>
public partial class MainViewModel : BaseViewModel
{
    private readonly IDialogService _dialogService;
    private readonly IConfigurationService _configurationService;
    private readonly DevExpressDemoViewModel _devExpressDemoViewModel;
    private readonly DispatcherTimer _timer;

    public MainViewModel(
        ILogger<MainViewModel> logger,
        IDialogService dialogService,
        IConfigurationService configurationService,
        DevExpressDemoViewModel devExpressDemoViewModel) : base(logger)
    {
        _dialogService = dialogService ?? throw new ArgumentNullException(nameof(dialogService));
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        _devExpressDemoViewModel = devExpressDemoViewModel ?? throw new ArgumentNullException(nameof(devExpressDemoViewModel));

        Title = "AirMonitor - DevExpress WPF 演示";

        // 初始化应用程序信息
        InitializeApplicationInfo();

        // 启动定时器更新当前时间
        _timer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _timer.Tick += (s, e) => CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        _timer.Start();

        CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        StatusMessage = "应用程序已就绪";
    }

    [ObservableProperty]
    private string _applicationName = string.Empty;

    [ObservableProperty]
    private string _applicationVersion = string.Empty;

    [ObservableProperty]
    private string _environment = string.Empty;

    [ObservableProperty]
    private string _currentTime = string.Empty;

    /// <summary>
    /// DevExpress演示ViewModel
    /// </summary>
    public DevExpressDemoViewModel DevExpressDemoViewModel => _devExpressDemoViewModel;

    [RelayCommand]
    private async Task NavigateToHomeAsync()
    {
        await ExecuteAsync(async () =>
        {
            // 这里可以添加导航到主页的逻辑
            StatusMessage = "已切换到主页";
            await Task.Delay(1000);
            StatusMessage = "应用程序已就绪";
        });
    }

    [RelayCommand]
    private async Task NavigateToDemoAsync()
    {
        await ExecuteAsync(async () =>
        {
            // 这里可以添加导航到演示页面的逻辑
            StatusMessage = "已切换到DevExpress演示页面";
            await Task.Delay(1000);
            StatusMessage = "应用程序已就绪";
        });
    }

    [RelayCommand]
    private async Task ShowSettingsAsync()
    {
        await _dialogService.ShowInfoAsync(
            "设置功能正在开发中...\n\n当前可用功能：\n- DevExpress主题切换\n- 数据网格操作\n- 图表显示",
            "设置");
    }

    [RelayCommand]
    private async Task ShowAboutAsync()
    {
        var aboutMessage = $"AirMonitor - DevExpress WPF 演示应用\n\n" +
                          $"版本: {ApplicationVersion}\n" +
                          $"环境: {Environment}\n\n" +
                          $"技术栈:\n" +
                          $"• .NET 8.0\n" +
                          $"• WPF + MVVM\n" +
                          $"• DevExpress WPF 24.2\n" +
                          $"• CommunityToolkit.Mvvm\n" +
                          $"• Microsoft.Extensions.DependencyInjection\n" +
                          $"• Serilog\n\n" +
                          $"这是一个展示如何在现代WPF应用程序中集成DevExpress控件的示例项目。";

        await _dialogService.ShowInfoAsync(aboutMessage, "关于 AirMonitor");
    }

    private void InitializeApplicationInfo()
    {
        try
        {
            var appSettings = _configurationService.AppSettings;
            ApplicationName = $"应用程序: {appSettings.Application.Name}";
            ApplicationVersion = $"版本: {appSettings.Application.Version}";
            Environment = $"环境: {appSettings.Application.Environment}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化应用程序信息时发生错误");
            ApplicationName = "应用程序: AirMonitor";
            ApplicationVersion = "版本: 1.0.0";
            Environment = "环境: Development";
        }
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _timer?.Stop();
        }
        base.Dispose(disposing);
    }
}
