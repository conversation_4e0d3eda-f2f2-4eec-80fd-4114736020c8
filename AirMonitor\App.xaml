﻿<Application
    x:Class="AirMonitor.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="clr-namespace:AirMonitor"
    xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- DevExpress Core Resources -->
                <ResourceDictionary Source="pack://application:,,,/DevExpress.Xpf.Core.v24.2;component/Themes/Generic/Generic.xaml" />

                <!-- DevExpress Office2019Colorful Theme -->
                <ResourceDictionary Source="pack://application:,,,/DevExpress.Xpf.Themes.Office2019Colorful.v24.2;component/Generic.xaml" />

                <!-- DevExpress Grid Resources -->
                <ResourceDictionary Source="pack://application:,,,/DevExpress.Xpf.Grid.v24.2;component/Themes/Generic/Generic.xaml" />

                <!-- DevExpress Charts Resources -->
                <ResourceDictionary Source="pack://application:,,,/DevExpress.Xpf.Charts.v24.2;component/Themes/Generic/Generic.xaml" />

                <!-- DevExpress Controls Resources -->
                <ResourceDictionary Source="pack://application:,,,/DevExpress.Xpf.Controls.v24.2;component/Themes/Generic/Generic.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Application-specific resources can be added here -->

        </ResourceDictionary>
    </Application.Resources>
</Application>
